# Android 專案重構計劃

## 概述
此文件記錄了將舊版 Android 專案升級到現代 Android 開發標準所需的所有變更。

## 已完成的修正 ✅

### 1. Namespace 問題
- **問題**: Android Gradle Plugin 8.0+ 需要明確指定 namespace
- **解決方案**: 在 `app/build.gradle` 和 `Core/build.gradle` 中添加 `namespace` 設定
- **狀態**: ✅ 完成

### 2. JVM 目標版本
- **問題**: Kotlin 編譯器 JVM 目標版本不一致
- **解決方案**: 統一設定為 Java 8
- **狀態**: ✅ 完成

### 3. 倉庫設定
- **問題**: 使用已棄用的 `jcenter()`
- **解決方案**: 移除 `jcenter()`，添加現代倉庫
- **狀態**: ✅ 完成

### 4. Glide 註解處理器
- **問題**: 使用 `annotationProcessor` 而非 `kapt`
- **解決方案**: 改為使用 `kapt`
- **狀態**: ✅ 完成

### 5. BuildConfig 功能
- **問題**: BuildConfig 類別未生成
- **解決方案**: 啟用 `buildFeatures.buildConfig = true`
- **狀態**: ✅ 完成

### 6. Kotlin Compose 插件
- **問題**: 不必要的 Compose 插件導致編譯錯誤
- **解決方案**: 移除 Compose 相關配置
- **狀態**: ✅ 完成

## 需要處理的問題 🔄

### 1. 缺少的依賴項替換

#### A. EasyRecyclerView (com.zhouyou:easyrecyclerview:1.0.5)
- **狀態**: ❌ 已移除，需要替換
- **替代方案**: 使用標準 `RecyclerView` + `SwipeRefreshLayout`
- **影響檔案**:
  - `MessageListFragment.kt`
  - `ServiceItemListFragment.kt`
  - `CustomRefreshHeader.java` (已移除)
  - `CustomMoreFooter.java` (已移除)

#### B. LiveEventBus (com.jeremyliao:live-event-bus-x:1.7.2)
- **狀態**: ❌ 已移除，需要替換
- **替代方案**: 使用 `LiveData` + `MutableLiveData` 或 `EventBus`
- **影響檔案**:
  - `MessageListFragment.kt`
  - `NotificationCollectorService.kt`

#### C. Calendar View (com.haibin:calendarview:3.6.7)
- **狀態**: 🔄 已替換為 `material-calendarview`
- **替代方案**: `com.github.prolificinteractive:material-calendarview:2.0.1`
- **影響檔案**:
  - `AppointmentListFragment.java`
  - `SimpleMonthView.java` (已移除)
  - `SimpleWeekView.java` (已移除)
  - `fragment_appointment_list.xml` (已更新)

### 2. 需要重構的檔案

#### A. MessageListFragment.kt
- **問題**: 依賴 `XRecyclerView` 和 `LiveEventBus`
- **解決方案**: 
  1. 替換 `XRecyclerView` 為標準 `RecyclerView`
  2. 添加 `SwipeRefreshLayout` 支援下拉刷新
  3. 替換 `LiveEventBus` 為 `LiveData`

#### B. ServiceItemListFragment.kt
- **問題**: 依賴 `XRecyclerView`
- **解決方案**: 同 MessageListFragment.kt

#### C. NotificationCollectorService.kt
- **問題**: 依賴 `LiveEventBus`
- **解決方案**: 使用 `LocalBroadcastManager` 或 `LiveData`

#### D. AppointmentListFragment.java
- **問題**: 依賴舊版日曆庫
- **解決方案**: 適配新的 `material-calendarview`

## 實施步驟

### 第一階段：基礎修正 (已完成)
1. ✅ 修正 namespace 問題
2. ✅ 統一 JVM 版本
3. ✅ 更新倉庫設定
4. ✅ 啟用 BuildConfig

### 第二階段：依賴項替換 (進行中)
1. 🔄 替換 EasyRecyclerView
2. 🔄 替換 LiveEventBus
3. 🔄 完成日曆庫遷移

### 第三階段：檔案重構
1. ❌ 重構 MessageListFragment.kt
2. ❌ 重構 ServiceItemListFragment.kt
3. ❌ 重構 NotificationCollectorService.kt
4. ❌ 重構 AppointmentListFragment.java

### 第四階段：測試與驗證
1. ❌ 編寫單元測試
2. ❌ 執行整合測試
3. ❌ 驗證功能完整性

## 技術債務

### 1. 版本更新
- Android Gradle Plugin: 8.10.1 (最新)
- Kotlin: 2.0.0 (可考慮更新到最新穩定版)
- Compile SDK: 33 (可考慮更新到 34)

### 2. 架構改進建議
- 考慮引入 Jetpack Compose (長期)
- 使用 Navigation Component
- 實施 MVVM 架構模式
- 添加依賴注入 (已使用 Hilt)

## 風險評估

### 高風險
- 日曆功能可能需要重新實現
- 訊息列表的下拉刷新功能需要重寫

### 中風險
- 服務項目列表功能
- 通知系統可能需要調整

### 低風險
- 基本 CRUD 操作
- 資料庫操作 (ObjectBox)

## 備註
- 所有移除的檔案都已備份在版本控制中
- 建議在每個階段完成後進行測試
- 保持向後相容性，避免破壞現有功能
