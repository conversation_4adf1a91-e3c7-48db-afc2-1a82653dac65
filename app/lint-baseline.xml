<?xml version="1.0" encoding="UTF-8"?>
<issues format="5" by="lint Chipmunk | 2021.2.1 Patch 1">

    <issue
        id="JcenterRepositoryObsolete"
        message="JCenter Maven repository is no longer receiving updates: newer library versions may be available elsewhere">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="JcenterRepositoryObsolete"
        message="JCenter Maven repository is no longer receiving updates: newer library versions may be available elsewhere">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="OldTargetApi"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details.">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="src/main/java/com/one/core/util/FormatUtils.java"
            line="23"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.tools.build:gradle than 7.1.2 is available: 7.1.3">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.2.0 is available: 1.5.0">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.recyclerview:recyclerview than 1.1.0 is available: 1.2.1">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.3.0 is available: 3.4.0">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.ext:junit than 1.1.2 is available: 1.1.3">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.android.billingclient:billing than 3.0.2 is available: 5.0.0">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.2.1 is available: 1.6.1">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.code.gson:gson than 2.8.6 is available: 2.9.0">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-analytics than 18.0.2 is available: 21.1.0">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-analytics than 18.0.2 is available: 21.1.0">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-auth than 20.0.2 is available: 21.0.7">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-config than 20.0.3 is available: 21.1.1">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-crashlytics than 17.3.1 is available: 18.2.12">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-crashlytics-gradle than 2.5.2 is available: 2.9.1">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-firestore than 22.0.2 is available: 24.2.2">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-inappmessaging-display than 19.1.4 is available: 20.1.2">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-messaging than 21.0.1 is available: 23.0.7">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.gms:google-services than 4.3.10 is available: 4.3.13">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.squareup.retrofit2:converter-gson than 2.5.0 is available: 2.9.0">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.squareup.retrofit2:retrofit than 2.5.0 is available: 2.9.0">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of io.objectbox:objectbox-gradle-plugin than 2.6.0 is available: 3.2.1">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of junit:junit than 4.13.1 is available: 4.13.2">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of org.permissionsdispatcher:permissionsdispatcher than 4.7.0 is available: 4.8.0">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of org.permissionsdispatcher:permissionsdispatcher-processor than 4.7.0 is available: 4.8.0">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="ExtraText"
        message="Unexpected text found in layout file: &quot;/&quot;">
        <location
            file="src/main/AndroidManifest.xml"
            line="4"/>
    </issue>

    <issue
        id="UselessParent"
        message="This `LinearLayout` layout or its `RelativeLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="src/main/res/layout/dialog_picker.xml"
            line="5"/>
    </issue>

    <issue
        id="IconXmlAndPng"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src/main/res/drawable-anydpi/ic_action_clear.xml, src/main/res/drawable-hdpi/ic_action_clear.png">
        <location
            file="src/main/res/drawable-xxhdpi/ic_action_clear.png"/>
        <location
            file="src/main/res/drawable-xhdpi/ic_action_clear.png"/>
        <location
            file="src/main/res/drawable-mdpi/ic_action_clear.png"/>
        <location
            file="src/main/res/drawable-hdpi/ic_action_clear.png"/>
        <location
            file="src/main/res/drawable-anydpi/ic_action_clear.xml"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;贊助&quot;, should use `@string` resource">
        <location
            file="src/main/res/layout/activity_donate.xml"
            line="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Done&quot;, should use `@string` resource">
        <location
            file="src/main/res/layout/dialog_picker.xml"
            line="32"/>
    </issue>

    <issue
        id="RelativeOverlap"
        message="`@id/tvTips` can overlap `@id/ibComment` if @string/app_activity_about_ratings_and_comments grows due to localized text expansion">
        <location
            file="src/main/res/layout/activity_about.xml"
            line="27"/>
    </issue>

    <issue
        id="RelativeOverlap"
        message="`TextView-1` can overlap `@id/ibIssueResponses` if @string/report_problem grows due to localized text expansion">
        <location
            file="src/main/res/layout/activity_about.xml"
            line="54"/>
    </issue>

    <issue
        id="RelativeOverlap"
        message="`@id/tvVersion` can overlap `ImageView-1` if @string/check_for_version_updates grows due to localized text expansion">
        <location
            file="src/main/res/layout/activity_about.xml"
            line="88"/>
    </issue>

</issues>
