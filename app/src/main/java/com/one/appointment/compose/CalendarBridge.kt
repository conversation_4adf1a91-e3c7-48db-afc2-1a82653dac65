package com.one.appointment.compose

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.appointment.entity.Appointment
import java.time.LocalDate

/**
 * Java 友好的接口
 */
interface DateSelectedListener {
    fun onDateSelected(date: LocalDate)
}

interface ViewModeChangedListener {
    fun onViewModeChanged(mode: CalendarViewMode)
}

interface AddAppointmentListener {
    fun onAddAppointment()
}

interface AppointmentDeleteListener {
    fun onAppointmentDelete(appointment: Appointment)
}

interface AppointmentClickListener {
    fun onAppointmentClick(appointment: Appointment)
}

/**
 * Kotlin 橋接類，用於在 Java Fragment 中設置 Compose Calendar
 */
object CalendarBridge {
    /**
     * 設置功能測試內容
     */
    fun setupFunctionTestContent(
        composeView: ComposeView,
        selectedDate: LocalDate,
        appointmentDates: Set<LocalDate>,
        dateListener: DateSelectedListener,
        viewModeListener: ViewModeChangedListener,
        addAppointmentListener: AddAppointmentListener
    ) {
        composeView.setContent {
            FunctionTestCalendarContent(
                selectedDate = selectedDate,
                appointmentDates = appointmentDates,
                onDateSelected = { date -> dateListener.onDateSelected(date) },
                onViewModeChanged = { mode -> viewModeListener.onViewModeChanged(mode) },
                onAddAppointment = { addAppointmentListener.onAddAppointment() }
            )
        }
    }

    /**
     * 設置完整的 Compose Calendar（生產版本）
     */
    fun setupProductionCalendarContent(
        composeView: ComposeView,
        selectedDate: LocalDate,
        appointmentDates: Set<LocalDate>,
        appointments: List<Appointment>,
        dateListener: DateSelectedListener,
        viewModeListener: ViewModeChangedListener,
        addAppointmentListener: AddAppointmentListener,
        deleteListener: AppointmentDeleteListener,
        clickListener: AppointmentClickListener
    ) {
        composeView.setContent {
            ResponsiveCalendarScreen(
                initialSelectedDate = selectedDate,
                appointments = appointments,
                initialAppointmentDates = appointmentDates,
                onDateSelected = { date -> dateListener.onDateSelected(date) },
                onViewModeChanged = { mode -> viewModeListener.onViewModeChanged(mode) },
                onAddAppointment = { addAppointmentListener.onAddAppointment() },
                onAppointmentDelete = { appointment -> deleteListener.onAppointmentDelete(appointment) },
                onAppointmentClick = { appointment -> clickListener.onAppointmentClick(appointment) }
            )
        }
    }
}

/**
 * 簡單的測試內容
 */
@Composable
internal fun TestCalendarContent() {
    MaterialTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text(
                    text = "🎉 Compose Calendar 測試",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    textAlign = TextAlign.Center
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "✅ Compose 已成功啟用",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(text = "• Material Design 3 主題", fontSize = 14.sp)
                    Text(text = "• Compose UI 組件", fontSize = 14.sp)
                    Text(text = "• 現代化設計語言", fontSize = 14.sp)

                    Spacer(modifier = Modifier.height(16.dp))

                    Button(
                        onClick = { /* 測試按鈕 */ },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("測試按鈕")
                    }
                }
            }
        }
    }
}

/**
 * 功能測試的 Compose 內容
 */
@Composable
internal fun FunctionTestCalendarContent(
    selectedDate: LocalDate,
    appointmentDates: Set<LocalDate>,
    onDateSelected: (LocalDate) -> Unit,
    onViewModeChanged: (CalendarViewMode) -> Unit,
    onAddAppointment: () -> Unit
) {
    MaterialTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 測試標題
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text(
                    text = "🧪 Compose Calendar 功能測試",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    textAlign = TextAlign.Center
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 當前選中日期顯示
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "📅 當前選中日期",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "${selectedDate.year}年 ${selectedDate.monthValue}月 ${selectedDate.dayOfMonth}日",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "預約數量: ${appointmentDates.size}",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 功能測試按鈕
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "🔧 功能測試",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // 日期選擇測試
                    Button(
                        onClick = {
                            onDateSelected(LocalDate.now())
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("📅 測試日期選擇 (選擇今天)")
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // 視圖模式測試
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = { onViewModeChanged(CalendarViewMode.MONTH) },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("月")
                        }
                        Button(
                            onClick = { onViewModeChanged(CalendarViewMode.WEEK) },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("週")
                        }
                        Button(
                            onClick = { onViewModeChanged(CalendarViewMode.YEAR) },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("年")
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // 添加預約測試
                    Button(
                        onClick = onAddAppointment,
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary
                        )
                    ) {
                        Text("➕ 測試添加預約")
                    }
                }
            }
        }
    }
}

/**
 * 響應式日曆螢幕 - 使用內部狀態管理
 */
@Composable
fun ResponsiveCalendarScreen(
    initialSelectedDate: LocalDate,
    appointments: List<Appointment>,
    initialAppointmentDates: Set<LocalDate>,
    onDateSelected: (LocalDate) -> Unit,
    onViewModeChanged: (CalendarViewMode) -> Unit,
    onAddAppointment: () -> Unit,
    onAppointmentDelete: (Appointment) -> Unit = {},
    onAppointmentClick: (Appointment) -> Unit = {}
) {
    // 使用 Compose 狀態管理
    var selectedDate by remember { mutableStateOf(initialSelectedDate) }
    var appointmentDates by remember { mutableStateOf(initialAppointmentDates) }
    var viewMode by remember { mutableStateOf(CalendarViewMode.MONTH) }

    // 當外部數據變化時更新內部狀態
    LaunchedEffect(initialSelectedDate) {
        selectedDate = initialSelectedDate
    }

    LaunchedEffect(initialAppointmentDates) {
        appointmentDates = initialAppointmentDates
    }

    AppointmentListScreen(
        selectedDate = selectedDate,
        appointments = appointments,
        appointmentDates = appointmentDates,
        viewMode = viewMode,
        onDateSelected = { date ->
            selectedDate = date
            onDateSelected(date)
        },
        onViewModeChanged = { mode ->
            viewMode = mode
            onViewModeChanged(mode)
        },
        onAddAppointment = onAddAppointment,
        onAppointmentDelete = onAppointmentDelete,
        onAppointmentClick = onAppointmentClick
    )
}
