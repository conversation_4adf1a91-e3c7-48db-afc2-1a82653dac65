package com.one.appointment.compose

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.appointment.entity.Appointment
import java.time.LocalDate
import java.util.Date
import java.util.Locale

/**
 * 預約列表主畫面 - 簡化版 Compose 實現
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppointmentListScreen(
    selectedDate: LocalDate = LocalDate.now(),
    appointments: List<Appointment>,
    appointmentDates: Set<LocalDate> = emptySet(),
    viewMode: CalendarViewMode = CalendarViewMode.MONTH,
    onDateSelected: (LocalDate) -> Unit = {},
    onViewModeChanged: (CalendarViewMode) -> Unit = {},
    onAddAppointment: () -> Unit = {},
    onAppointmentClick: (Appointment) -> Unit = {},
    onAppointmentDelete: (Appointment) -> Unit = {}
) {
    // 應用程式配色
    val backgroundColor = AppColors.Background

    // 滑動狀態管理
    val listState = rememberLazyListState()
    var isFabVisible by remember { mutableStateOf(true) }

    // 檢測滑動方向
    LaunchedEffect(listState) {
        var previousIndex = 0
        var previousScrollOffset = 0

        snapshotFlow {
            Pair(listState.firstVisibleItemIndex, listState.firstVisibleItemScrollOffset)
        }.collect { (currentIndex, currentScrollOffset) ->
            if (previousIndex != currentIndex) {
                isFabVisible = currentIndex <= previousIndex
            } else {
                isFabVisible = currentScrollOffset <= previousScrollOffset
            }
            previousIndex = currentIndex
            previousScrollOffset = currentScrollOffset
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 8.dp) // 頂部留白避免陰影被裁剪
        ) {
            // Compose 日曆 - 固定高度，添加陰影空間
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp) // 左右留白給陰影空間
            ) {
                CalendarCard(
                    selectedDate = selectedDate,
                    appointmentDates = appointmentDates,
                    viewMode = viewMode,
                    onDateSelected = onDateSelected,
                    onViewModeChanged = onViewModeChanged
                )
            }

            Spacer(modifier = Modifier.height(10.dp))

            // 預約列表卡片 - 佔用剩餘空間，添加陰影空間
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(
                        start = 8.dp,
                        end = 8.dp,
                        bottom = 20.dp // 增加底部留白給浮動按鈕和陰影
                    )
            ) {
                AppointmentListCard(
                    selectedDate = selectedDate,
                    appointments = appointments,
                    listState = listState,
                    onAppointmentClick = onAppointmentClick,
                    onAppointmentDelete = onAppointmentDelete
                )
            }
        }
    }
}

/**
 * 預約列表卡片 - 優化版
 */
@Composable
fun AppointmentListCard(
    selectedDate: LocalDate,
    appointments: List<Appointment>,
    listState: LazyListState,
    onAppointmentClick: (Appointment) -> Unit,
    onAppointmentDelete: (Appointment) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxSize()
            .padding(
                horizontal = 8.dp, // 減少水平邊距，因為外層已有 padding
                vertical = 4.dp    // 添加垂直邊距給陰影空間
            ),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = AppColors.Surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp,
            pressedElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(4.dp) // 內部邊距
        ) {
            // 優化的標題列
            AppointmentListHeader(
                selectedDate = selectedDate,
                appointmentCount = appointments.size
            )

            // 分隔線
            if (appointments.isNotEmpty()) {
                HorizontalDivider(
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 12.dp),
                    color = AppColors.TextSecondary.copy(alpha = 0.1f),
                    thickness = 1.dp
                )
            }

            // 預約列表容器 - 智能高度調整，根據項目數量動態設定
            val itemHeight = 90.dp // 每個預約項目的大概高度（包含間距）
            val maxVisibleItems = 10 // 最多顯示5個項目，超過則滾動
            val calculatedHeight = remember(appointments.size) {
                val baseHeight = 140.dp // 基礎高度（包含 padding 和間距）
                val contentHeight = (appointments.size * itemHeight.value).dp
                val maxHeight = (maxVisibleItems * itemHeight.value + baseHeight.value).dp

                when {
                    appointments.isEmpty() -> 200.dp // 空狀態固定高度
                    appointments.size <= maxVisibleItems -> contentHeight + baseHeight // 完整顯示
                    else -> maxHeight // 限制最大高度，啟用滾動
                }
            }

            Box(
                modifier = Modifier
                    .height(calculatedHeight)
                    .fillMaxWidth()
                    .padding(bottom = 0.dp) // 額外的底部邊距
            ) {
                if (appointments.isEmpty()) {
                    EmptyAppointmentState()
                } else {
                    AppointmentList(
                        appointments = appointments,
                        listState = listState,
                        onAppointmentClick = onAppointmentClick,
                        onAppointmentDelete = onAppointmentDelete
                    )
                }
            }
        }
    }
}

/**
 * 預約列表標題 - 優化版
 */
@Composable
fun AppointmentListHeader(
    selectedDate: LocalDate,
    appointmentCount: Int
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .padding(top = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左側標題區域
        Column {
            Text(
                text = "今日預約",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = AppColors.TextPrimary
            )

            // 日期顯示
            Text(
                text = formatSelectedDate(selectedDate),
                fontSize = 14.sp,
                color = AppColors.TextSecondary,
                modifier = Modifier.padding(top = 2.dp)
            )
        }

        // 右側預約數量 Chip - 優化設計
        Surface(
            shape = RoundedCornerShape(20.dp),
            color = if (appointmentCount > 0) AppColors.Primary else AppColors.TextSecondary.copy(alpha = 0.2f),
            modifier = Modifier
                .clip(RoundedCornerShape(20.dp))
        ) {
            Row(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 圖標
                Text(
                    text = if (appointmentCount > 0) "📅" else "📭",
                    fontSize = 16.sp
                )

                Spacer(modifier = Modifier.width(6.dp))

                // 數量文字
                Text(
                    text = appointmentCount.toString(),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = if (appointmentCount > 0) Color.White else AppColors.TextSecondary
                )
            }
        }
    }
}

/**
 * 空狀態顯示
 */
@Composable
fun EmptyAppointmentState() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(10.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 空狀態圖標
            Surface(
                shape = CircleShape,
                color = AppColors.Primary.copy(alpha = 0.1f),
                modifier = Modifier.size(100.dp)
            ) {
                Box(
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = "📅",
                        fontSize = 48.sp
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 主要文字
            Text(
                text = "今日暫無預約",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = AppColors.TextPrimary,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 副文字
            Text(
                text = "點擊右下角按鈕新增預約",
                fontSize = 16.sp,
                color = AppColors.TextSecondary,
                textAlign = TextAlign.Center,
                lineHeight = 24.sp
            )

//            Spacer(modifier = Modifier.height(8.dp))

//            // 提示卡片
//            Card(
//                shape = RoundedCornerShape(16.dp),
//                colors = CardDefaults.cardColors(
//                    containerColor = AppColors.Primary.copy(alpha = 0.05f)
//                ),
//                modifier = Modifier.fillMaxWidth()
//            ) {
//                Row(
//                    modifier = Modifier.padding(16.dp),
//                    verticalAlignment = Alignment.CenterVertically
//                ) {
//                    Text(
//                        text = "💡",
//                        fontSize = 20.sp
//                    )
//
//                    Spacer(modifier = Modifier.width(12.dp))
//
//                    Text(
//                        text = "您可以為客戶安排預約，管理服務項目和時間",
//                        fontSize = 14.sp,
//                        color = AppColors.TextSecondary,
//                        lineHeight = 20.sp
//                    )
//                }
//            }
        }
    }
}

/**
 * 預約列表 - 優化版
 */
@Composable
fun AppointmentList(
    appointments: List<Appointment>,
    listState: LazyListState,
    onAppointmentClick: (Appointment) -> Unit,
    onAppointmentDelete: (Appointment) -> Unit
) {
    Box(modifier = Modifier.fillMaxSize()) {
        LazyColumn(
            state = listState, // 使用傳入的 listState
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            contentPadding = PaddingValues(
                top = 8.dp,
                bottom = 40.dp // 增加底部留白，確保最後一項不被遮擋
            ),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(
                items = appointments,
                key = { appointment -> appointment.id }
            ) { appointment ->
                AppointmentItem(
                    appointment = appointment,
                    onClick = { onAppointmentClick(appointment) },
                    onDelete = { onAppointmentDelete(appointment) }
                )
            }
        }

        // 滾動指示器 - 當有更多內容時顯示
        val canScrollDown by remember {
            derivedStateOf {
                listState.canScrollForward
            }
        }

        if (canScrollDown && appointments.size > 3) {
            // 底部滾動提示
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 16.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f),
                        shape = RoundedCornerShape(16.dp)
                    )
                    .padding(horizontal = 12.dp, vertical = 6.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "向下滾動",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(16.dp)
                    )
                    Text(
                        text = "還有 ${appointments.size - 3} 個預約",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }
    }
}

/**
 * 預約項目 - 優化版
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppointmentItem(
    appointment: Appointment,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = AppColors.Background
        ),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左側時間指示器和狀態
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 時間指示器
                Surface(
                    shape = CircleShape,
                    color = AppColors.Primary,
                    modifier = Modifier.size(16.dp)
                ) {}

                // 連接線
                Box(
                    modifier = Modifier
                        .width(2.dp)
                        .height(16.dp)
                        .background(
                            AppColors.Primary.copy(alpha = 0.3f),
                            RoundedCornerShape(1.dp)
                        )
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 預約信息區域
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 預約標題
                Text(
                    text = getAppointmentDisplayTitle(appointment),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = AppColors.TextPrimary,
                    maxLines = 1
                )

                Spacer(modifier = Modifier.height(4.dp))

                // 時間顯示
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "🕐",
                        fontSize = 14.sp
                    )

                    Spacer(modifier = Modifier.width(4.dp))

                    Text(
                        text = formatAppointmentTime(appointment.time),
                        fontSize = 14.sp,
                        color = AppColors.TextSecondary,
                        fontWeight = FontWeight.Medium
                    )
                }

                // 服務項目（如果有）
                val serviceText = getServiceItemsText(appointment)
                if (serviceText.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(4.dp))

                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "✂️",
                            fontSize = 12.sp
                        )

                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = serviceText,
                            fontSize = 12.sp,
                            color = AppColors.TextSecondary,
                            maxLines = 1
                        )
                    }
                }
            }

            // 右側操作按鈕
            Surface(
                onClick = onDelete,
                shape = CircleShape,
                color = AppColors.White,
                modifier = Modifier.size(40.dp)
            ) {
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier.fillMaxSize()
                ) {
                    Text(
                        text = "🗑️",
                        fontSize = 18.sp
                    )
                }
            }
        }
    }
}

/**
 * 格式化選中日期
 */
private fun formatSelectedDate(date: LocalDate): String {
    return try {
        val formatter = java.time.format.DateTimeFormatter.ofPattern("MM月dd日 EEEE", Locale.getDefault())
        date.format(formatter)
    } catch (e: Exception) {
        "日期未知"
    }
}

/**
 * 獲取預約顯示標題
 */
private fun getAppointmentDisplayTitle(appointment: Appointment): String {
    return try {
        val customer = appointment.customer.target
        if (customer != null && !customer.name.isNullOrEmpty()) {
            customer.name
        } else {
            "預約客戶"
        }
    } catch (e: Exception) {
        "預約客戶"
    }
}

/**
 * 獲取服務項目文字
 */
private fun getServiceItemsText(appointment: Appointment): String {
    return try {
        val serviceItems = appointment.serviceItemList
        if (serviceItems.isNotEmpty()) {
            serviceItems.take(2).joinToString(", ") { it.name ?: "服務" }
        } else {
            ""
        }
    } catch (e: Exception) {
        ""
    }
}

/**
 * 格式化預約時間
 */
private fun formatAppointmentTime(timestamp: Long): String {
    return try {
        val date = Date(timestamp)
        val formatter = java.text.SimpleDateFormat("HH:mm", Locale.getDefault())
        formatter.format(date)
    } catch (e: Exception) {
        "時間未知"
    }
}

/**
 * 預覽
 */
@Preview(showBackground = true)
@Composable
fun AppointmentListScreenPreview() {
    MaterialTheme {
        AppointmentListScreen(appointments = emptyList())
    }
}
