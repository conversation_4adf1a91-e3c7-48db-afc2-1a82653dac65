package com.one.appointment.repositories

import com.google.gson.Gson
import com.one.appointment.ObjectBox
import com.one.appointment.entity.Appointment
import com.one.appointment.entity.UploadData
import com.one.appointment.entity.UploadData.*
import dagger.Reusable
import javax.inject.Inject

@Reusable
class CustomerRepository @Inject constructor() {

    fun saveCustomerData(): String {
        val mAppointmentBox = ObjectBox.get().boxFor(
            Appointment::class.java
        )
        val appointmentList = mAppointmentBox.query().build().find()
        val uploadData = UploadData()
        val appointmentDataList: MutableList<AppointmentData> = ArrayList()
        for (i in appointmentList.indices) {
            val appointment = appointmentList[i]
            val appointmentData = AppointmentData()
            appointmentData.time = appointment.time
            val customerData = CustomerData()
            customerData.birthday = appointment.customer.target.birthday
            customerData.gender = appointment.customer.target.gender
            customerData.name = appointment.customer.target.name
            customerData.nickName = appointment.customer.target.nickName
            customerData.phoneNumber = appointment.customer.target.phoneNumber
            customerData.avatarImagePath = appointment.customer.target.avatarImagePath
            appointmentData.customer = customerData
            val serviceItemDataList: MutableList<ServiceItemData> = ArrayList()
            for (j in appointment.serviceItemList.indices) {
                val serviceItem = appointment.serviceItemList[j]
                val serviceItemData = ServiceItemData()
                serviceItemData.description = serviceItem.description
                serviceItemData.name = serviceItem.name
                serviceItemData.number = serviceItem.number
                serviceItemData.price = serviceItem.price
                serviceItemData.serviceTime = serviceItem.serviceTime
                serviceItemDataList.add(serviceItemData)
            }
            appointmentData.serviceItemList = serviceItemDataList
            appointmentDataList.add(appointmentData)
        }
        uploadData.appointmentList = appointmentDataList
        val gson = Gson()
        return gson.toJson(uploadData)
    }

    companion object {
        fun saveCustomerData(): String {
            val mAppointmentBox = ObjectBox.get().boxFor(
                Appointment::class.java
            )
            val appointmentList = mAppointmentBox.query().build().find()
            val uploadData = UploadData()
            val appointmentDataList: MutableList<AppointmentData> = ArrayList()
            for (i in appointmentList.indices) {
                val appointment = appointmentList[i]
                val appointmentData = AppointmentData()
                appointmentData.time = appointment.time
                val customerData = CustomerData()
                customerData.birthday = appointment.customer.target.birthday
                customerData.gender = appointment.customer.target.gender
                customerData.name = appointment.customer.target.name
                customerData.nickName = appointment.customer.target.nickName
                customerData.phoneNumber = appointment.customer.target.phoneNumber
                customerData.avatarImagePath = appointment.customer.target.avatarImagePath
                appointmentData.customer = customerData
                val serviceItemDataList: MutableList<ServiceItemData> = ArrayList()
                for (j in appointment.serviceItemList.indices) {
                    val serviceItem = appointment.serviceItemList[j]
                    val serviceItemData = ServiceItemData()
                    serviceItemData.description = serviceItem.description
                    serviceItemData.name = serviceItem.name
                    serviceItemData.number = serviceItem.number
                    serviceItemData.price = serviceItem.price
                    serviceItemData.serviceTime = serviceItem.serviceTime
                    serviceItemDataList.add(serviceItemData)
                }
                appointmentData.serviceItemList = serviceItemDataList
                appointmentDataList.add(appointmentData)
            }
            uploadData.appointmentList = appointmentDataList
            val gson = Gson()
            return gson.toJson(uploadData)
        }
    }
}