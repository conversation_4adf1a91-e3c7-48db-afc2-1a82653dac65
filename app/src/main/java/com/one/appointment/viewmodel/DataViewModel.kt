package com.one.appointment.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.one.appointment.repositories.CustomerRepository
import com.one.appointment.util.DriveServiceHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DataViewModel @Inject constructor(
    private val customerRepository: CustomerRepository
) : ViewModel() {

    fun save(driveServiceHelper: DriveServiceHelper) {
        viewModelScope.launch {
            val json = customerRepository.saveCustomerData()
            driveServiceHelper.saveFile("1", "appointmentList", json)

//            val fileMetadata = File()
//            fileMetadata.setName("config.json")
//            fileMetadata.setParents(Collections.singletonList("appDataFolder"))
//            val filePath = File("files/config.json")
//            val mediaContent = FileContent("application/json", filePath)
//            val file: File = driveService.files().create(fileMetadata, mediaContent)
//                .setFields("id")
//                .execute()
//            System.out.println("File ID: " + file.getId())
        }
    }

}