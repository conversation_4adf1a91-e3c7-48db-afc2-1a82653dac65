package com.one.appointment

import android.app.NotificationChannel
import android.app.NotificationChannelGroup
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.multidex.MultiDex
import androidx.multidex.MultiDexApplication
import com.one.appointment.service.NotificationCollectorMonitorService
import com.one.appointment.util.GoogleServicesUtil
import com.one.appointment.util.NotificationUtil.toggleNotificationListenerService
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class App : MultiDexApplication() {
    override fun onCreate() {
        super.onCreate()

        // 檢查 Google Play Services 可用性
        checkGooglePlayServices()

        // 初始化 ObjectBox
        ObjectBox.init(this)
        // 每次啟動時檢測 NotificationListenerService 是否生效，不生效重新開啟
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            startForegroundService(new Intent(this, NotificationCollectorMonitorService.class));
//        } else {
//            startService(new Intent(this, NotificationCollectorMonitorService.class));
//        }
        try {
            startService(Intent(this, NotificationCollectorMonitorService::class.java))
        } catch (e: Exception) {
            e.printStackTrace()
            toggleNotificationListenerService(this)
        }
        createNotificationChannel()
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    private fun createNotificationChannel() {
        // 聊天室訊息
        val channelMessage =
            NotificationChannel(
                getString(R.string.channel_message_id),
                getString(R.string.channel_message_name),
                NotificationManager.IMPORTANCE_HIGH
            )
        val channelAlarm =
            NotificationChannel(
                getString(R.string.channel_alarm_id),
                getString(R.string.channel_alarm_name),
                NotificationManager.IMPORTANCE_HIGH
            )

        val notificationManager: NotificationManager =
            this.getSystemService(NotificationManager::class.java)

        val notificationChannelGroup = NotificationChannelGroup(
            getString(R.string.channel_group_id),
            getString(R.string.channel_group_name)
        )

        notificationManager.createNotificationChannelGroup(notificationChannelGroup)

        channelMessage.group = getString(R.string.channel_group_id)
        channelAlarm.group = getString(R.string.channel_group_id)

        notificationManager.createNotificationChannel(channelMessage)
        notificationManager.createNotificationChannel(channelAlarm)
    }

    /**
     * 檢查 Google Play Services 可用性
     */
    private fun checkGooglePlayServices() {
        try {
            val status = GoogleServicesUtil.getGoogleServicesStatusDescription(this)
            Log.d("App", "Google Services 狀態: $status")

            if (GoogleServicesUtil.isRunningOnEmulator()) {
                Log.i("App", "在模擬器中運行，Google Services 錯誤是正常的")
            }

            GoogleServicesUtil.safeInitializeGoogleServices(
                context = this,
                onSuccess = {
                    Log.d("App", "Google Services 初始化成功")
                },
                onFailure = {
                    Log.w("App", "Google Services 初始化失敗，但不影響核心功能")
                }
            )
        } catch (e: SecurityException) {
            GoogleServicesUtil.handleGoogleServicesSecurityException(e)
        } catch (e: Exception) {
            Log.e("App", "檢查 Google Play Services 時發生錯誤: ${e.message}")
        }
    }
}