package com.one.appointment.entity;

import java.util.List;

public class UploadData {

    private List<AppointmentData> appointmentList;
    private List<CustomerData> customerList;
    private List<ServiceItemData> serviceItemList;

    public static class AppointmentData {
        public long time;
        public CustomerData customer;
        public List<ServiceItemData> serviceItemList;
    }

    public static class CustomerData {
        public String name;
        public String nickName;
        public String avatarImagePath;
        public String phoneNumber;
        public String birthday;
        public String gender;
    }

    public static class ServiceItemData {
        public String name;
        public int price;
        public int serviceTime;
        public String description;
        public int number; // 可以顏色區分
    }

    public void setAppointmentList(List<AppointmentData> appointmentList) {
        this.appointmentList = appointmentList;
    }

    public void setCustomerList(List<CustomerData> customerList) {
        this.customerList = customerList;
    }

    public void setServiceItemList(List<ServiceItemData> serviceItemList) {
        this.serviceItemList = serviceItemList;
    }

    public List<AppointmentData> getAppointmentList() {
        return appointmentList;
    }

    public List<CustomerData> getCustomerList() {
        return customerList;
    }

    public List<ServiceItemData> getServiceItemList() {
        return serviceItemList;
    }
}
