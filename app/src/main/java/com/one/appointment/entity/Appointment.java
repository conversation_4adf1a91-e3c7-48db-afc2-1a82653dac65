package com.one.appointment.entity;


import java.io.Serializable;

import io.objectbox.BoxStore;
import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;
import io.objectbox.relation.ToMany;
import io.objectbox.relation.ToOne;

@Entity
public class Appointment implements Serializable {

    @Id
    public long id;
    private long time;
    private ToOne<Customer> customer = new ToOne<>(this, Appointment_.customer);
    private ToMany<ServiceItem> serviceItemList = new ToMany<>(this, Appointment_.serviceItemList);
    // normally transformer would add field, but need to manually for local unit tests
    transient BoxStore __boxStore;

    public long getId() {
        return id;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public ToOne<Customer> getCustomer() {
        return customer;
    }

    public void setCustomer(ToOne<Customer> customer) {
        this.customer = customer;
    }

    public ToMany<ServiceItem> getServiceItemList() {
        return serviceItemList;
    }

    public void setServiceItemList(ToMany<ServiceItem> serviceItemList) {
        this.serviceItemList = serviceItemList;
    }

}
