package com.one.appointment.firebase;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings;
import com.one.appointment.R;

public class RemoteConfigUtil {

    public interface CallBack {
        void onComplete(Task<Void> task, FirebaseRemoteConfig firebaseRemoteConfig);
    }

    // 檢查版本
    public static void fetchConfig(Activity activity, CallBack callBack) {
        FirebaseRemoteConfig firebaseRemoteConfig = FirebaseRemoteConfig.getInstance();
        FirebaseRemoteConfigSettings configSettings = new FirebaseRemoteConfigSettings.Builder()
                .setMinimumFetchIntervalInSeconds(3)
                .build();
        firebaseRemoteConfig.setConfigSettingsAsync(configSettings);
        firebaseRemoteConfig.setDefaultsAsync(R.xml.remote_config_defaults);
        // cache expiration in seconds
        long cacheExpiration = 0; //3600 == 1 hour
        firebaseRemoteConfig.fetch(cacheExpiration)
                .addOnCompleteListener(activity, new OnCompleteListener<Void>() {
                    @Override
                    public void onComplete(@NonNull Task<Void> task) {
                        if (task.isSuccessful()) {
                            // After config data is successfully fetched, it must be activated before newly fetched values are returned.
                            //Make the values available to your app
                            firebaseRemoteConfig.activate();
                        }
                        callBack.onComplete(task, firebaseRemoteConfig);
                    }
                });

    }
}
