package com.one.appointment.activity

import android.Manifest
import android.os.Build
import android.os.Bundle
import com.one.core.util.LogUtil

/**
 * 權限處理活動基類
 * 已更新為使用現代 Android Activity Result API
 * 替代已棄用的 permissions.dispatcher
 */
abstract class PermissionActivity : ModernPermissionActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onResume() {
        super.onResume()
        LogUtil.d("onResume")
    }

    override fun onPause() {
        super.onPause()
        LogUtil.i("onPause")
    }

    // ========== 重寫現代權限處理方法 ==========

    /**
     * 用戶允許權限
     */
    override fun onPermissionsGranted() {
        super.onPermissionsGranted()
        LogUtil.i("permissions: 用戶允許權限")
        isAllowPermissions = true
        showPermissionsAllow()
    }

    /**
     * 向用戶說明為什麼需要這些權限
     */
    override fun onShouldShowRequestPermissionRationale(
        permissions: List<String>, 
        onProceed: () -> Unit
    ) {
        LogUtil.i("permissions: 向用戶說明為什麼需要這些權限")
        onShowRationale(onProceed)
    }

    /**
     * 用戶拒絕授權回調
     */
    override fun onPermissionsDenied(deniedPermissions: List<String>) {
        super.onPermissionsDenied(deniedPermissions)
        LogUtil.i("permissions: 用戶拒絕")
        onPermissionDenied()
    }

    /**
     * 用戶勾選了"不再提醒"時調用
     */
    override fun onPermissionsPermanentlyDenied(permanentlyDeniedPermissions: List<String>) {
        super.onPermissionsPermanentlyDenied(permanentlyDeniedPermissions)
        LogUtil.i("permissions: 不再提醒")
        onNeverAskAgain()
    }

    // ========== 保持向後相容的方法 ==========

    /**
     * 用戶允許權限 (向後相容)
     */
    open fun showPermissionsAllow() {
        LogUtil.i("permissions: 用戶允許權限")
        isAllowPermissions = true
    }

    /**
     * 向用戶說明為什麼需要這些權限 (向後相容)
     */
    open fun onShowRationale(onProceed: () -> Unit) {
        LogUtil.i("permissions: 向用戶說明為什麼需要這些權限")
        onProceed()
    }

    /**
     * 用戶拒絕授權回調 (向後相容)
     */
    open fun onPermissionDenied() {
        LogUtil.i("permissions: 用戶拒絕")
        // Toast.makeText(this, R.string.permission_camera_denied, Toast.LENGTH_SHORT).show()
    }

    /**
     * 用戶勾選了"不再提醒"時調用 (向後相容)
     */
    open fun onNeverAskAgain() {
        LogUtil.i("permissions: 不再提醒")
        // Toast.makeText(this, R.string.permission_camera_neverask, Toast.LENGTH_SHORT).show()
    }

    /**
     * 獲取需要的權限列表
     * 根據 Android 版本返回適當的權限
     */
    override fun getRequiredPermissions(): List<String> {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE -> {
                // Android 14+ (API 34+) 使用細分的媒體權限
                listOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_AUDIO
                )
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                // Android 13 (API 33) 使用細分的媒體權限
                listOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_AUDIO
                )
            }
            else -> {
                // Android 12 及以下使用傳統存儲權限
                listOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }
        }
    }
}
