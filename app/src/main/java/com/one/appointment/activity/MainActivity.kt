package com.one.appointment.activity


import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.PopupMenu
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.widget.Toolbar
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.Scope
import com.google.api.client.extensions.android.http.AndroidHttp
import com.google.api.client.googleapis.extensions.android.gms.auth.GoogleAccountCredential
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.drive.Drive
import com.google.api.services.drive.DriveScopes
import com.one.appointment.BuildConfig
import com.one.appointment.R
import com.one.appointment.constant.KeyDefine
import com.one.appointment.databinding.ActivityMainBinding
import com.one.appointment.util.DriveServiceHelper
import com.one.appointment.util.LogUtil
import com.one.appointment.viewmodel.DataViewModel
import com.one.core.activity.AboutActivity
import com.one.core.activity.BaseActivity
import com.one.core.activity.DonateActivity
import com.one.core.data.RemoteConfig



class MainActivity : BaseActivity() {

    private var itemIndex: Int = 0
    private var mDriveServiceHelper: DriveServiceHelper? = null
    private var type = 0

    private val viewModel by viewModels<DataViewModel>()
    lateinit var binding: ActivityMainBinding

    private val navHostFragment by lazy {
        supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
    }

    private val navController by lazy {
        navHostFragment.navController
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()

        // Android 33 需判斷
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            askPermission(android.Manifest.permission.POST_NOTIFICATIONS)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        initView()
    }

    override fun initParams(bundle: Bundle?) {
        if (bundle != null) {
            itemIndex = bundle.getInt(KeyDefine.INDEX, 0)
            when (itemIndex) {
                0 -> {
                    navController.navigate(R.id.appointmentList)
                }
                1 -> {
                    navController.navigate(R.id.messageListFragment)
                }
            }

        }
    }


    private fun initView() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)

        setupBottomNavigation()

        navController.addOnDestinationChangedListener { _: NavController?, _: NavDestination?, _: Bundle? -> }
        binding.bottomNavigation.menu.clear()
        binding.bottomNavigation.inflateMenu(R.menu.bottom_navigation_menu)
        binding.bottomNavigation.menu.getItem(itemIndex).isChecked = true
    }

    private fun setupBottomNavigation() {
        binding.bottomNavigation.setupWithNavController(navController)
        binding.bottomNavigation.setOnItemSelectedListener {
            when (it.itemId) {
                R.id.appointmentList -> {
                    navController.navigate(R.id.appointmentList)
                }
                R.id.messageList -> {
                    navController.navigate(R.id.messageListFragment)
                }
                R.id.customerList -> {
                    navController.navigate(R.id.customerListFragment)
                }
                R.id.serviceItemList -> {
                    navController.navigate(R.id.serviceItemListFragment)
                }
                else -> {
                    LogUtil.e("未定義按鈕")
                }
            }
            true
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        // Handle item selection
        if (item.itemId == R.id.action_settings) {
            showPopupMenu(activity.findViewById(R.id.action_settings))
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    fun showPopupMenu(view: View?) {
        val popupMenu = PopupMenu(activity, view)
        popupMenu.menuInflater.inflate(R.menu.setting_menu, popupMenu.menu)
        popupMenu.show()
        if (!RemoteConfig.isOpenBilling) {
            popupMenu.menu.getItem(0).isVisible = false
        }
        popupMenu.setOnMenuItemClickListener { item: MenuItem ->
            when (item.itemId) {
                R.id.action_menu_billing -> startActivity(
                    DonateActivity::class.java
                )

                R.id.action_menu_share -> {
                    val intentShare = Intent(Intent.ACTION_SEND)
                    intentShare.type = "text/plain"
                    intentShare.putExtra(Intent.EXTRA_SUBJECT, "分享")
                    intentShare.putExtra(
                        Intent.EXTRA_TEXT,
                        """
                        預約本
                        https://play.google.com/store/apps/details?id=$packageName
                        """.trimIndent()
                    )
                    startActivity(Intent.createChooser(intentShare, "Appointment"))
                }

                R.id.action_menu_setting_format -> startActivity(SettingsActivity::class.java)
                R.id.action_menu_about -> {
                    val bundle = Bundle()
                    bundle.putInt(AboutActivity.ARG_VERSION_CODE, BuildConfig.VERSION_CODE)
                    val intent = Intent()
                    intent.setClass(this, AboutActivity::class.java)
                    intent.putExtras(bundle)
                    startActivity(intent)
                }
                R.id.action_menu_upload -> upload()
                R.id.action_menu_download -> download()
                else -> {}
            }
            true
        }
        popupMenu.setOnDismissListener { menu: PopupMenu? -> }
        popupMenu.show()
    }

    private fun upload() {
        type = 1
        requestSignIn()
    }

    private fun download() {
        type = 2
        requestSignIn()
    }

    override fun onBackPressed() {
        if (!navController.popBackStack()) {
            val dialog = MaterialDialog(this, MaterialDialog.DEFAULT_BEHAVIOR)
            dialog.message(null, "是否要退出本程式?", null)
            dialog.positiveButton(null, "是") { _: MaterialDialog? ->
                finish()
            }
            dialog.negativeButton(null, "否") { _: MaterialDialog? ->
                LogUtil.d()
                dialog.dismiss()
            }
            dialog.show()
        }
    }

    private fun requestSignIn() {
        val signInOptions = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestEmail()
            .requestScopes(
                Scope(DriveScopes.DRIVE_FILE),
                Scope(DriveScopes.DRIVE_APPDATA)
            )
            .requestIdToken("405273120733-7oh7rejes193ellr0545om650o70pdq6.apps.googleusercontent.com")
            .build()
        val client = GoogleSignIn.getClient(activity, signInOptions)
        activityResultLauncher.launch(client.signInIntent)
    }

    // You can do the assignment inside onAttach or onCreate, i.e, before the activity is displayed
    private var activityResultLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == REQUEST_CODE_SIGN_IN) {
            handleSignInResult(result.data)
        }
    }

    private fun handleSignInResult(result: Intent?) {
        GoogleSignIn.getSignedInAccountFromIntent(result)
            .addOnSuccessListener { googleAccount: GoogleSignInAccount ->
//                    Log.d(TAG, "Signed in as " + googleAccount.getEmail());
                val credential = GoogleAccountCredential.usingOAuth2(
                    activity, setOf(DriveScopes.DRIVE_FILE)
                )
                credential.selectedAccount = googleAccount.account
                val googleDriveService = Drive.Builder(
                    AndroidHttp.newCompatibleTransport(),
                    GsonFactory(),
                    credential
                )
                    .setApplicationName(getString(R.string.app_name))
                    .build()

                // The DriveServiceHelper encapsulates all REST API and SAF functionality.
                // Its instantiation is required before handling any onClick actions.
                mDriveServiceHelper = DriveServiceHelper(googleDriveService)
                when (type) {
                    1 -> {
                        viewModel.save(mDriveServiceHelper!!)
                    }

                    2 -> {
                        val fileListTask = mDriveServiceHelper!!.queryFiles()
                        //                            fileListTask.getResult().getFiles().get(0)
                        val pairTask = mDriveServiceHelper!!.readFile("1")
                        val pairTaskResult = pairTask.result
                        LogUtil.d(pairTaskResult.first)
                        //                            pairTaskResult.first
                        val resultFile = fileListTask.result
                    }
                }
            }
            .addOnFailureListener { e: Exception -> e.printStackTrace() }
    }

    companion object {
        private const val REQUEST_CODE_SIGN_IN = 1000
    }
}