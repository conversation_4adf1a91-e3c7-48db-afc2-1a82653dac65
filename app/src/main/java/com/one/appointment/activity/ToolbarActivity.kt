package com.one.appointment.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.navigation.fragment.NavHostFragment
import com.one.appointment.R
import com.one.appointment.constant.EntryPoint
import com.one.appointment.databinding.ActivityToolbarBinding
import com.one.core.activity.BaseActivity

class ToolbarActivity : BaseActivity() {
    private var title: String? = null
    private var entryPoint = 0

    lateinit var binding: ActivityToolbarBinding

    private val navHostFragment by lazy {
        supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
    }

    private val navController by lazy {
        navHostFragment.navController
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityToolbarBinding.inflate(layoutInflater)
        setContentView(binding.root)
        navController.popBackStack()
        val bundle = intent.extras

        binding.tvTitle.text = title
        when (entryPoint) {
            EntryPoint.AddCustomerFragment -> {
                binding.tvTitle.text = "顧客資料"
                navController.navigate(R.id.addCustomerFragment, bundle)
           }
            EntryPoint.AddAppointmentFragment -> {
                binding.tvTitle.text = "預約"
                navController.navigate(R.id.addAppointmentFragment, bundle)
            }
            EntryPoint.ServiceItemFragment -> {
                binding.tvTitle.text = "服務項目"
                navController.navigate(R.id.serviceItemListFragment, bundle)
            }
            EntryPoint.CustomerListFragment -> {
                binding.tvTitle.text = "顧客列表"
                navController.navigate(R.id.customerListFragment, bundle)
           }
            EntryPoint.AddServiceFragment -> {
                binding.tvTitle.text = "服務項目"
                navController.navigate(R.id.addServiceFragment, bundle)
           }
            EntryPoint.AppointmentRecordFragment -> {
                binding.tvTitle.text = "顧客預約紀錄"
                navController.navigate(R.id.appointmentRecordFragment, bundle)
            }
            else -> {}
        }
    }

    override fun initParams(bundle: Bundle?) {
        if (bundle == null) {
            return
        }
        title = bundle.getString("title", "")
        entryPoint = bundle.getInt(KEY_ENTRY_POINT, -1)
    }

    companion object {
        var KEY_ENTRY_POINT = "EntryPoint"

        @JvmStatic
        fun toActivity(context: Context, entryPoint: Int, bundle: Bundle) {
            val intent = Intent()
            intent.setClass(context, ToolbarActivity::class.java)
            bundle.putInt(KEY_ENTRY_POINT, entryPoint)
            intent.putExtras(bundle)
            context.startActivity(intent)
        }
    }
}