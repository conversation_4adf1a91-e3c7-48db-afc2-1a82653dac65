package com.one.appointment.activity


import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.text.TextUtils
import com.afollestad.materialdialogs.MaterialDialog
import com.google.android.gms.tasks.Task
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.one.appointment.BuildConfig
import com.one.appointment.R
import com.one.appointment.firebase.RemoteConfigUtil
import com.one.appointment.util.LogUtil
import com.one.appointment.util.NotificationUtil
import com.one.core.data.RemoteConfig
import com.one.core.firebase.FCMUtil

@SuppressLint("CustomSplashScreen")
class SplashActivity : PermissionActivity() {

    private var mStartTime: Long = 0 // 開始時間
    private var mHandler: Handler? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        hasPermission(true)
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)
        mStartTime = System.currentTimeMillis()
        FCMUtil.logFCMToken()

        // 如果權限檢查沒有被觸發（例如已經有權限），直接檢查並進入主頁
        if (hasAllRequiredPermissions()) {
            isAllowPermissions = true
            LogUtil.i("SplashActivity: 已有所有權限，直接進入主頁")
        }
    }


    override fun onResume() {
        super.onResume()
        LogUtil.d("onResume")
        FCMUtil.checkGooglePlayServices(this)
        // 檢查權限狀態，如果已有權限則直接進入主頁
        if (hasAllRequiredPermissions()) {
            isAllowPermissions = true
            postDelayed()
        }
    }

    override fun initParams(bundle: Bundle?) {

    }

    // ========== 現代權限處理回調 ==========

    /**
     * 權限授予時的回調
     */
    override fun showPermissionsAllow() {
        super.showPermissionsAllow()
        LogUtil.i("SplashActivity: 權限已授予，準備進入主頁")
        postDelayed()
    }

    /**
     * 權限拒絕時的回調
     */
    override fun onPermissionDenied() {
        super.onPermissionDenied()
        LogUtil.e("SplashActivity: 權限被拒絕")
        // 可以選擇顯示說明對話框或直接進入主頁（功能受限）
        // 這裡我們選擇直接進入主頁
        postDelayed()
    }

    /**
     * 權限被永久拒絕時的回調
     */
    override fun onNeverAskAgain() {
        super.onNeverAskAgain()
        LogUtil.e("SplashActivity: 權限被永久拒絕")
        // 可以顯示引導用戶到設定頁面的對話框
        // 這裡我們選擇直接進入主頁（功能受限）
        postDelayed()
    }

    private fun postDelayed() {
        mHandler = Handler()
        mHandler!!.postDelayed({
            val loadingTime = System.currentTimeMillis() - mStartTime // 計算一下總共花費的時間
            if (loadingTime < SHOW_TIME_MIN) { // 如果比最小顯示時間還短，就延時進入MainActivity，否則直接進入
                mHandler!!.postDelayed(
                    goToMainActivity, SHOW_TIME_MIN
                            - loadingTime
                )
            } else {
                mHandler!!.post(goToMainActivity)
            }
        }, SHOW_TIME_MIN.toLong())
    }

    // 舊的 onRequestPermissionsResult 已移除
    // 現在使用現代的 Activity Result API 處理權限

    // 檢查版本
    private var goToMainActivity = Runnable { checkVersionConfig() }

    // 檢查版本
    private fun checkVersionConfig() {
        RemoteConfigUtil.fetchConfig(this) { task: Task<Void?>, firebaseRemoteConfig: FirebaseRemoteConfig ->
            if (task.isSuccessful) {
                // get value from remote config
                RemoteConfig.adPosition = firebaseRemoteConfig.getLong("adPosition")
                RemoteConfig.isOpenAd = firebaseRemoteConfig.getBoolean("isOpenAd")
                RemoteConfig.isOpenBilling = firebaseRemoteConfig.getBoolean("isOpenBilling")
                RemoteConfig.adType = firebaseRemoteConfig.getLong("ad_type")
                val isForceUpdate = firebaseRemoteConfig.getBoolean("force_update")
                val versionCode = firebaseRemoteConfig.getString("version_code")
                val apkURL = firebaseRemoteConfig.getString("apk_url")
                if (!TextUtils.isEmpty(versionCode) && versionCode.toInt() > BuildConfig.VERSION_CODE) {
                    val dialog =
                        MaterialDialog(this@SplashActivity, MaterialDialog.DEFAULT_BEHAVIOR)
                    dialog.title(null, "檢查版本更新")
                    dialog.message(null, "已有新版本是否進行更新?", null)
                    dialog.positiveButton(null, "是") {
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(apkURL))
                        startActivity(intent)
                    }
                    dialog.negativeButton(null, "否") {
                        LogUtil.d()
                        if (isForceUpdate) {
                            finish()
                        } else {
                            showMaterialDialog()
                        }
                    }
                    dialog.show()
                } else {
                    showMaterialDialog()
                }
            } else {
                // 無法取得 Remote Config
                showMaterialDialog()
            }
        }
    }

    private fun showMaterialDialog() {
        if (NotificationUtil.isNotificationListenersEnabled(appCompatActivity)) {
            toMainActivity()
            return
        }
        val dialog = MaterialDialog(this@SplashActivity, MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.title(null, "提醒")
        dialog.message(null, "開啟通知存取權限方能接收預約訊息", null)
        dialog.positiveButton(null, "開啟") {
            NotificationUtil.enabledNotificationListeners(
                appCompatActivity
            )
        }
        dialog.negativeButton(null, "取消") {
            toMainActivity()
        }
        dialog.show()
    }

    private fun toMainActivity() {
        val intentMainActivity = Intent(this, MainActivity::class.java)
        intentMainActivity.flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
        intentMainActivity.putExtras(intent)
        intentMainActivity.action = intent.action
        finish()
        startActivity(intentMainActivity)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)

        val intentMainActivity = Intent(this, MainActivity::class.java)
        intentMainActivity.flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
        intentMainActivity.putExtras(intent)
        intentMainActivity.action = intent.action
        finish()
        startActivity(intentMainActivity)
    }
    companion object {
        private const val SHOW_TIME_MIN = 300 // 最小顯示時間
    }
}