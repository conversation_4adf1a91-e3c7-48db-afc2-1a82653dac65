package com.one.appointment.activity


import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.Window
import com.one.appointment.ObjectBox
import com.one.appointment.R
import com.one.appointment.constant.PrefKey
import com.one.appointment.databinding.ActivitySettingsBinding
import com.one.appointment.entity.ServiceItem
import com.one.appointment.entity.ServiceItem_
import com.one.appointment.util.FormatUtils
import com.one.appointment.util.PrefUtils
import com.one.core.activity.BaseActivity
import java.util.*

class SettingsActivity : BaseActivity(), View.OnClickListener {
    private lateinit var binding: ActivitySettingsBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 不顯示label 要在setContentView之前呼叫
        requestWindowFeature(Window.FEATURE_NO_TITLE)

        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

//        setContentView(R.layout.activity_settings)

        binding.btSaveKeyWord.setOnClickListener(this)
        binding.btShareFormat.setOnClickListener(this)
        binding.btShareItem.setOnClickListener(this)
        binding.btReplyToAppointment.setOnClickListener(this)
        val keyWord =
            PrefUtils.getFromPrefs(this, PrefKey.KEY_WORD, getString(R.string.appointment))
        binding. etKeyWord.setText(keyWord)
    }

    override fun initParams(bundle: Bundle?) {}
    override fun onClick(view: View) {
        val keyWord =  binding.etKeyWord.text.toString()
        when (view.id) {
            R.id.btSaveKeyWord -> PrefUtils.saveToPrefs(
                appCompatActivity,
                PrefKey.KEY_WORD,
                keyWord
            )
            R.id.btShareFormat -> {
                val calendar = Calendar.getInstance()
                val timeZone = TimeZone.getTimeZone("GMT+8")
                calendar.timeZone = timeZone
                calendar.time
                val time = FormatUtils.dateToString(calendar.time, "yyyy/MM/dd HH:mm")
                val serviceItemBox = ObjectBox.get().boxFor(
                    ServiceItem::class.java
                )
                val serviceItemList = serviceItemBox.all
                var serviceItemName = "服務項目A+服務項目B"
                if (serviceItemList != null && serviceItemList.size > 0) {
                    serviceItemName = serviceItemList[0].name
                    if (serviceItemList.size > 1) {
                        serviceItemName = serviceItemName + "+" + serviceItemList[1].name
                    }
                }
                val intentShare = Intent(Intent.ACTION_SEND)
                intentShare.type = "text/plain"
                intentShare.putExtra(Intent.EXTRA_SUBJECT, "預約格式")
                val format =
                    getString(R.string.refer_format) + keyWord + " " + time + " " + serviceItemName
                intentShare.putExtra(Intent.EXTRA_TEXT, format)
                startActivity(Intent.createChooser(intentShare, "Appointment"))
            }
            R.id.btShareItem -> {
                val mServiceItemBox = ObjectBox.get().boxFor(
                    ServiceItem::class.java
                )
                val list = mServiceItemBox.query().orderDesc(ServiceItem_.id).build().find()
                val itemList = StringBuilder("服務項目如下：\n")
                var i = 0
                while (i < list.size) {
                    val serviceItem = list[i]
                    itemList.append(serviceItem.name).append(" ").append(serviceItem.price)
                        .append("元 ").append(serviceItem.serviceTime).append("分鐘").append("\n")
                    i++
                }
                val intentShare = Intent(Intent.ACTION_SEND)
                intentShare.type = "text/plain"
                intentShare.putExtra(Intent.EXTRA_SUBJECT, "預約")
                intentShare.putExtra(
                    Intent.EXTRA_TEXT,
                    itemList.toString()
                )
                startActivity(Intent.createChooser(intentShare, "Make Appointment"))
            }
            R.id.btReplyToAppointment -> {
                val intentShare = Intent(Intent.ACTION_SEND)
                intentShare.type = "text/plain"
                intentShare.putExtra(Intent.EXTRA_SUBJECT, "預約")
                intentShare.putExtra(
                    Intent.EXTRA_TEXT,
                    "預約成功!"
                )
                startActivity(Intent.createChooser(intentShare, "Make Appointment"))
            }
        }
    }
}