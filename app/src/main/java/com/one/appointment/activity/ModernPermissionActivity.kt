package com.one.appointment.activity

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import com.one.core.activity.BaseActivity
import com.one.core.util.LogUtil

/**
 * 現代權限處理基類
 * 使用 Android Activity Result API 替代已棄用的 permissions.dispatcher
 */
abstract class ModernPermissionActivity : BaseActivity() {
    
    private var mIsCheckPermission = false
    var isAllowPermissions: Boolean = false
    
    // 權限請求啟動器
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        handlePermissionResult(permissions)
    }
    
    // 單個權限請求啟動器
    private val singlePermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        handleSinglePermissionResult(isGranted)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (mIsCheckPermission) {
            checkAndRequestPermissions()
        }
    }
    
    override fun hasPermission(isCheckPermission: Boolean) {
        mIsCheckPermission = isCheckPermission
    }
    
    /**
     * 檢查並請求權限
     */
    private fun checkAndRequestPermissions() {
        val permissions = getRequiredPermissions()
        val deniedPermissions = permissions.filter { !hasPermission(it) }
        
        if (deniedPermissions.isEmpty()) {
            // 所有權限都已授予
            onPermissionsGranted()
        } else {
            // 請求缺少的權限
            requestPermissions(deniedPermissions)
        }
    }
    
    /**
     * 獲取需要的權限列表
     * 子類可以重寫此方法來指定需要的權限
     * 根據 Android 版本返回適當的權限
     */
    protected open fun getRequiredPermissions(): List<String> {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE -> {
                // Android 14+ (API 34+) 使用細分的媒體權限
                listOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_AUDIO
                )
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                // Android 13 (API 33) 使用細分的媒體權限
                listOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_AUDIO
                )
            }
            else -> {
                // Android 12 及以下使用傳統存儲權限
                listOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }
        }
    }
    
    /**
     * 檢查是否有指定權限
     */
    private fun hasPermission(permission: String): Boolean {
        return ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 請求多個權限
     */
    private fun requestPermissions(permissions: List<String>) {
        // 檢查是否需要顯示權限說明
        val shouldShowRationale = permissions.any { shouldShowRequestPermissionRationale(it) }
        
        if (shouldShowRationale) {
            onShouldShowRequestPermissionRationale(permissions) {
                permissionLauncher.launch(permissions.toTypedArray())
            }
        } else {
            permissionLauncher.launch(permissions.toTypedArray())
        }
    }
    
    /**
     * 請求單個權限
     */
    fun requestSinglePermission(permission: String) {
        if (hasPermission(permission)) {
            onSinglePermissionGranted(permission)
        } else if (shouldShowRequestPermissionRationale(permission)) {
            onShouldShowRequestPermissionRationale(listOf(permission)) {
                singlePermissionLauncher.launch(permission)
            }
        } else {
            singlePermissionLauncher.launch(permission)
        }
    }
    
    /**
     * 處理權限請求結果
     */
    private fun handlePermissionResult(permissions: Map<String, Boolean>) {
        val grantedPermissions = permissions.filterValues { it }.keys
        val deniedPermissions = permissions.filterValues { !it }.keys
        
        LogUtil.i("權限結果 - 已授予: $grantedPermissions, 已拒絕: $deniedPermissions")
        
        if (deniedPermissions.isEmpty()) {
            // 所有權限都已授予
            onPermissionsGranted()
        } else {
            // 有權限被拒絕
            onPermissionsDenied(deniedPermissions.toList())
            
            // 檢查是否有權限被永久拒絕
            val permanentlyDeniedPermissions = deniedPermissions.filter { 
                !shouldShowRequestPermissionRationale(it) 
            }
            
            if (permanentlyDeniedPermissions.isNotEmpty()) {
                onPermissionsPermanentlyDenied(permanentlyDeniedPermissions.toList())
            }
        }
    }
    
    /**
     * 處理單個權限請求結果
     */
    private fun handleSinglePermissionResult(isGranted: Boolean) {
        if (isGranted) {
            LogUtil.i("單個權限已授予")
            onSinglePermissionGranted("")
        } else {
            LogUtil.i("單個權限被拒絕")
            onSinglePermissionDenied("")
        }
    }
    
    // ========== 回調方法，子類可以重寫 ==========
    
    /**
     * 所有權限都已授予時調用
     */
    protected open fun onPermissionsGranted() {
        LogUtil.i("所有權限都已授予")
        isAllowPermissions = true
    }
    
    /**
     * 有權限被拒絕時調用
     */
    protected open fun onPermissionsDenied(deniedPermissions: List<String>) {
        LogUtil.i("權限被拒絕: $deniedPermissions")
        isAllowPermissions = false
    }
    
    /**
     * 有權限被永久拒絕時調用
     */
    protected open fun onPermissionsPermanentlyDenied(permanentlyDeniedPermissions: List<String>) {
        LogUtil.i("權限被永久拒絕: $permanentlyDeniedPermissions")
        // 可以在這裡引導用戶到設定頁面
    }
    
    /**
     * 需要顯示權限說明時調用
     */
    protected open fun onShouldShowRequestPermissionRationale(
        permissions: List<String>, 
        onProceed: () -> Unit
    ) {
        LogUtil.i("需要顯示權限說明: $permissions")
        // 默認直接繼續請求權限
        onProceed()
    }
    
    /**
     * 單個權限授予時調用
     */
    protected open fun onSinglePermissionGranted(permission: String) {
        LogUtil.i("單個權限已授予: $permission")
    }
    
    /**
     * 單個權限拒絕時調用
     */
    protected open fun onSinglePermissionDenied(permission: String) {
        LogUtil.i("單個權限被拒絕: $permission")
    }
    
    // ========== 便利方法 ==========
    
    /**
     * 檢查是否有所有必需的權限
     */
    fun hasAllRequiredPermissions(): Boolean {
        return getRequiredPermissions().all { hasPermission(it) }
    }
    
    /**
     * 重新檢查權限
     */
    fun recheckPermissions() {
        checkAndRequestPermissions()
    }
}
