package com.one.appointment.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.one.appointment.util.NotificationUtil;

public class AlarmReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {

        Bundle bundle = intent.getBundleExtra("data");
//        String name = (String) bundle.get("title");
//        String item = (String) bundle.get("item");
//        NotificationUtil.createAlarmNotification(context, name, item);

        if (bundle != null) {
            //主要執行的程式
            String name = bundle.getString("title") + "預約時間到";
            String item = bundle.getString("item");
            NotificationUtil.createAlarmNotification(context, name, item);

//            Appointment appointment = (Appointment) bundle.get(KeyDefine.APPOINTMENT);
//            Appointment appointment = (Appointment) bundle.getSerializable(KeyDefine.APPOINTMENT);
//            if (appointment != null) {
//                ToOne<Customer> customerToOne = appointment.getCustomer();
//
//                StringBuilder item = new StringBuilder();
//                for (int i = 0; i < appointment.getServiceItemList().size(); i++) {
//                    item.append(appointment.getServiceItemList().get(i));
//                }
//                NotificationUtil.createAlarmNotification(context, customerToOne.getTarget().getName(), item.toString());
//            }

        }
    }
}