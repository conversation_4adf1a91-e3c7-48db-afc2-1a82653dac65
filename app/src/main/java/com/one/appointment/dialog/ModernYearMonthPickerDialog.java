package com.one.appointment.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.one.appointment.R;

import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;

/**
 * 現代化的年月選擇器對話框 - 基於 View 實現
 */
public class ModernYearMonthPickerDialog {
    
    public interface OnYearMonthSelectedListener {
        void onYearMonthSelected(YearMonth yearMonth);
    }
    
    private Context context;
    private Dialog dialog;
    private YearMonth selectedYearMonth;
    private OnYearMonthSelectedListener listener;
    
    // Adapters
    private YearAdapter yearAdapter;
    private MonthAdapter monthAdapter;
    
    public ModernYearMonthPickerDialog(Context context) {
        this.context = context;
        this.selectedYearMonth = YearMonth.now();
    }
    
    public void show(YearMonth currentYearMonth, OnYearMonthSelectedListener listener) {
        this.selectedYearMonth = currentYearMonth;
        this.listener = listener;
        
        createDialog();
        dialog.show();
    }
    
    private void createDialog() {
        // 創建自定義佈局
        View dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_year_month_picker, null);
        
        // 初始化視圖
        TextView titleText = dialogView.findViewById(R.id.titleText);
        TextView selectedYearMonthText = dialogView.findViewById(R.id.selectedYearMonthText);
        RecyclerView yearRecyclerView = dialogView.findViewById(R.id.yearRecyclerView);
        RecyclerView monthRecyclerView = dialogView.findViewById(R.id.monthRecyclerView);
        
        // 設定標題
        titleText.setText("選擇年月");
        updateSelectedYearMonthText(selectedYearMonthText);
        
        // 設定年份 RecyclerView
        setupYearRecyclerView(yearRecyclerView, selectedYearMonthText);
        
        // 設定月份 RecyclerView
        setupMonthRecyclerView(monthRecyclerView, selectedYearMonthText);
        
        // 創建對話框
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(context);
        builder.setView(dialogView);
        builder.setPositiveButton("確定", (dialog, which) -> {
            if (listener != null) {
                listener.onYearMonthSelected(selectedYearMonth);
            }
        });
        builder.setNegativeButton("取消", null);
        
        dialog = builder.create();
    }
    
    private void setupYearRecyclerView(RecyclerView recyclerView, TextView selectedText) {
        List<Integer> years = new ArrayList<>();
        int currentYear = YearMonth.now().getYear();
        for (int i = currentYear - 10; i <= currentYear + 10; i++) {
            years.add(i);
        }
        
        yearAdapter = new YearAdapter(years, selectedYearMonth.getYear(), year -> {
            selectedYearMonth = YearMonth.of(year, selectedYearMonth.getMonthValue());
            updateSelectedYearMonthText(selectedText);
            yearAdapter.setSelectedYear(year);
        });
        
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        recyclerView.setAdapter(yearAdapter);
        
        // 滾動到選中的年份
        int selectedPosition = years.indexOf(selectedYearMonth.getYear());
        if (selectedPosition >= 0) {
            recyclerView.scrollToPosition(selectedPosition);
        }
    }
    
    private void setupMonthRecyclerView(RecyclerView recyclerView, TextView selectedText) {
        List<Integer> months = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            months.add(i);
        }
        
        monthAdapter = new MonthAdapter(months, selectedYearMonth.getMonthValue(), month -> {
            selectedYearMonth = YearMonth.of(selectedYearMonth.getYear(), month);
            updateSelectedYearMonthText(selectedText);
            monthAdapter.setSelectedMonth(month);
        });
        
        recyclerView.setLayoutManager(new GridLayoutManager(context, 3));
        recyclerView.setAdapter(monthAdapter);
    }
    
    private void updateSelectedYearMonthText(TextView textView) {
        textView.setText(selectedYearMonth.getYear() + "年 " + selectedYearMonth.getMonthValue() + "月");
    }
    
    public void dismiss() {
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }
    
    public boolean isShowing() {
        return dialog != null && dialog.isShowing();
    }
    
    // 年份 Adapter
    private static class YearAdapter extends RecyclerView.Adapter<YearAdapter.YearViewHolder> {
        
        public interface OnYearClickListener {
            void onYearClick(int year);
        }
        
        private List<Integer> years;
        private int selectedYear;
        private OnYearClickListener listener;
        
        public YearAdapter(List<Integer> years, int selectedYear, OnYearClickListener listener) {
            this.years = years;
            this.selectedYear = selectedYear;
            this.listener = listener;
        }
        
        @NonNull
        @Override
        public YearViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_year_picker, parent, false);
            return new YearViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(@NonNull YearViewHolder holder, int position) {
            int year = years.get(position);
            holder.bind(year, year == selectedYear, listener);
        }
        
        @Override
        public int getItemCount() {
            return years.size();
        }
        
        public void setSelectedYear(int year) {
            this.selectedYear = year;
            notifyDataSetChanged();
        }
        
        static class YearViewHolder extends RecyclerView.ViewHolder {
            TextView yearText;
            
            public YearViewHolder(@NonNull View itemView) {
                super(itemView);
                yearText = itemView.findViewById(R.id.yearText);
            }
            
            public void bind(int year, boolean isSelected, OnYearClickListener listener) {
                yearText.setText(String.valueOf(year));
                yearText.setSelected(isSelected);
                
                if (isSelected) {
                    yearText.setTextColor(itemView.getContext().getResources().getColor(R.color.colorPrimary));
                    yearText.setBackgroundResource(R.drawable.selected_year_background);
                } else {
                    yearText.setTextColor(itemView.getContext().getResources().getColor(R.color.text_primary));
                    yearText.setBackgroundResource(R.drawable.normal_year_background);
                }
                
                itemView.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onYearClick(year);
                    }
                });
            }
        }
    }
    
    // 月份 Adapter
    private static class MonthAdapter extends RecyclerView.Adapter<MonthAdapter.MonthViewHolder> {
        
        public interface OnMonthClickListener {
            void onMonthClick(int month);
        }
        
        private List<Integer> months;
        private int selectedMonth;
        private OnMonthClickListener listener;
        private String[] monthNames = {"1月", "2月", "3月", "4月", "5月", "6月",
                                      "7月", "8月", "9月", "10月", "11月", "12月"};
        
        public MonthAdapter(List<Integer> months, int selectedMonth, OnMonthClickListener listener) {
            this.months = months;
            this.selectedMonth = selectedMonth;
            this.listener = listener;
        }
        
        @NonNull
        @Override
        public MonthViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_month_picker, parent, false);
            return new MonthViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(@NonNull MonthViewHolder holder, int position) {
            int month = months.get(position);
            holder.bind(month, monthNames[month - 1], month == selectedMonth, listener);
        }
        
        @Override
        public int getItemCount() {
            return months.size();
        }
        
        public void setSelectedMonth(int month) {
            this.selectedMonth = month;
            notifyDataSetChanged();
        }
        
        static class MonthViewHolder extends RecyclerView.ViewHolder {
            TextView monthText;
            
            public MonthViewHolder(@NonNull View itemView) {
                super(itemView);
                monthText = itemView.findViewById(R.id.monthText);
            }
            
            public void bind(int month, String monthName, boolean isSelected, OnMonthClickListener listener) {
                monthText.setText(monthName);
                monthText.setSelected(isSelected);
                
                if (isSelected) {
                    monthText.setTextColor(itemView.getContext().getResources().getColor(R.color.white));
                    monthText.setBackgroundResource(R.drawable.selected_month_background);
                } else {
                    monthText.setTextColor(itemView.getContext().getResources().getColor(R.color.text_primary));
                    monthText.setBackgroundResource(R.drawable.normal_month_background);
                }
                
                itemView.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onMonthClick(month);
                    }
                });
            }
        }
    }
}
