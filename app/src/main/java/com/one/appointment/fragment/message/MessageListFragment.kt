package com.one.appointment.fragment.message

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.one.appointment.ObjectBox
import com.one.appointment.R
import com.one.appointment.activity.ToolbarActivity.Companion.toActivity
import com.one.appointment.constant.EntryPoint
import com.one.appointment.constant.KeyDefine
import com.one.appointment.databinding.FragmentMessageListBinding
import com.one.appointment.entity.Message
import com.one.appointment.entity.Message_
import com.one.appointment.fragment.BaseFragment
import com.one.appointment.fragment.message.adapter.MessageAdapter
import com.one.appointment.util.FormatUtils
import com.one.appointment.util.ImageUtil
import com.one.core.ad.FacebookAdHelp
import com.one.core.ad.GoogleAdHelp
import io.objectbox.Box
import java.util.Calendar
import java.util.TimeZone

/**
 * MessageListFragment
 */
class MessageListFragment : BaseFragment() {
    //R.layout.fragment_message_list
    private var mRecyclerView: RecyclerView? = null
    private var mSwipeRefreshLayout: SwipeRefreshLayout? = null
    private var mAdapter: MessageAdapter? = null
    private var mMessageBox: Box<Message>? = null
    private lateinit var binding: FragmentMessageListBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMessageListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        appCompatActivity.title = getString(R.string.message)
        if (appCompatActivity.supportActionBar != null) {
            appCompatActivity.supportActionBar!!.show()
        }
        mMessageBox = ObjectBox.get().boxFor(Message::class.java)
        mRecyclerView = view.findViewById(R.id.recyclerView)
        mSwipeRefreshLayout = view.findViewById(R.id.swipeRefreshLayout)
        binding.layoutEmpty.tvEmptyDesc.text = "尚未接收到訊息"
        initRecycleView()
        if (mSwipeRefreshLayout != null) {
            mSwipeRefreshLayout!!.isRefreshing = true
        }
        // TODO: 替換 LiveEventBus 為 LiveData 或其他方案
        // LiveEventBus
        //     .get(KeyDefine.POST_MESSAGE, Bundle::class.java)
        //     .observeSticky(this) { initData() }

        // 暫時直接載入資料
        initData()

        initAd()
    }

    override fun initView(view: View) {}

    private fun initData() {
        mAdapter!!.clearList()
        data
    }

    private fun initAd() {
        GoogleAdHelp.addView(
            requireContext(),
            "ca-app-pub-1800606262336792/5337776640",
            binding.bannerContainer
        )
        FacebookAdHelp.addView(
            requireContext(),
            "521662492234245_758316028568889",
            binding.bannerContainer
        )
    }

    private val data: Unit
        get() {
            val messageList = mMessageBox!!.query().orderDesc(Message_.postTime).build().find()
            if (messageList.size == 0) {
                binding.layoutEmpty.tvEmptyDesc.visibility = View.VISIBLE
            } else {
                binding.layoutEmpty.tvEmptyDesc.visibility = View.GONE
            }
            mAdapter!!.addList(messageList)
            // 停止刷新動畫
            mSwipeRefreshLayout?.isRefreshing = false
        }

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(activity)
        layoutManager.orientation = RecyclerView.VERTICAL
        mRecyclerView!!.layoutManager = layoutManager
        val mMessageBox = ObjectBox.get().boxFor(Message::class.java)
        val messageList = mMessageBox.query().order(Message_.postTime).build().find()
        mAdapter = MessageAdapter(activity, messageList) { view -> showPopupMenu(view) }
        mRecyclerView!!.adapter = mAdapter

        // 設定下拉刷新
        mSwipeRefreshLayout!!.setOnRefreshListener {
            //refresh data here
            initData()
        }
    }

    private fun showPopupMenu(view: View) {
        val message = view.tag as Message
        val popupMenu = PopupMenu(context, view)
        popupMenu.menuInflater.inflate(R.menu.message_menu, popupMenu.menu)
        popupMenu.show()
        var bundle = Bundle()
        popupMenu.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.addCustomers -> {

                    bundle.putString(KeyDefine.NICK_NAME, message.title)
                    bundle.putString(
                        KeyDefine.IMAGE_PATH, ImageUtil.getImagePath(
                            appCompatActivity, message.packageName, message.title
                        )
                    )
                    toActivity(appCompatActivity, EntryPoint.AddCustomerFragment, bundle)
                }
                R.id.delete -> {
                    mMessageBox!!.remove(message)
                    initData()
                }
                R.id.addAppointment -> {
                    bundle = Bundle()
                    bundle.putString(KeyDefine.NICK_NAME, message.title)
                    bundle.putString(
                        KeyDefine.IMAGE_PATH, ImageUtil.getImagePath(
                            appCompatActivity, message.packageName, message.title
                        )
                    )
                    if (message.packageName == "tw.hairbook.photo") {
                        parserStyleMap(message, bundle)
                    } else {
                        val messageContent =
                            message.content.replace(getString(R.string.refer_format), "")
                        val contentArray = messageContent.split(" ").toTypedArray()
                        if (contentArray.size == 2) {
                            if (contentArray[1].contains("/")) {
                                val date = FormatUtils.stringToDate(
                                    contentArray[1], "yyyy/MM/dd"
                                )
                                bundle.putSerializable(KeyDefine.DATE, date)
                            }
                        }
                        if (contentArray.size > 2) {
                            contentArray[2] = contentArray[2].replace("：", ":")
                            if (contentArray[1].contains("/") && contentArray[2].contains(":")) {
                                val date = FormatUtils.stringToDate(
                                    contentArray[1] + " " + contentArray[2], "yyyy/MM/dd HH:mm"
                                )
                                bundle.putSerializable(KeyDefine.DATE, date)
                            }
                        }
                        if (contentArray.size > 3) {
//                            String[] itemArray = contentArray[3].split("/+");
                            bundle.putString(KeyDefine.SERVICE_ITEM, contentArray[3])
                        }
                        toActivity(appCompatActivity, EntryPoint.AddAppointmentFragment, bundle)
                    }
                }
                else -> {}
            }
            true
        }
        popupMenu.setOnDismissListener {
            // 控件消失时的事件
        }
        popupMenu.show()
    }

    private fun parserStyleMap(message: Message, bundle: Bundle) {
        // 想與您預約 05/22 (週五) 16:00 的修瀏海 服務
        val calendar = Calendar.getInstance()
        val timeZone = TimeZone.getTimeZone("GMT+8")
        calendar.timeZone = timeZone
        val year = calendar[Calendar.YEAR]
        val contentArray = message.content.split(" ").toTypedArray()
        if (contentArray.size == 6) {
            var date = FormatUtils.stringToDate(
                year.toString() + "/" + contentArray[1] + " " + contentArray[3],
                "yyyy/MM/dd HH:mm"
            )
            val calendarAppointment = Calendar.getInstance()
            calendarAppointment.time = date
            if (calendarAppointment.before(calendar)) {
                date = FormatUtils.stringToDate(
                    (year + 1).toString() + "/" + contentArray[1] + " " + contentArray[3],
                    "yyyy/MM/dd HH:mm"
                )
            }
            bundle.putSerializable(KeyDefine.DATE, date)
            bundle.putString(KeyDefine.SERVICE_ITEM, contentArray[4].replace("的", ""))
        }
        toActivity(appCompatActivity, EntryPoint.AddAppointmentFragment, bundle)
    }
}