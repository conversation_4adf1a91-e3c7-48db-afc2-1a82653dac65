package com.one.appointment.fragment.customer.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.one.appointment.R;
import com.one.appointment.entity.Customer;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.List;

public class CustomerItemQuickAdapter extends BaseQuickAdapter<Customer, BaseViewHolder> {

    private final Context mContext;
    private List<Customer> mList;
    private View.OnClickListener mOnClickListener;
    private View.OnClickListener mOnItemClickListener;

    public CustomerItemQuickAdapter(Context context, List<Customer> list) {
        super(R.layout.item_customer, list);
        mContext = context;
        mList = list;
    }



    public void setList(List<Customer> houseList) {
        mList = houseList;
        notifyDataSetChanged();
    }

    public void clearList() {
        mList.clear();
        notifyDataSetChanged();
    }

    public void addList(List<Customer> list) {
        if (list == null) {
            return;
        }
        mList.addAll(list);
        notifyDataSetChanged();
    }

//    @NonNull
//    @Override
//    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
//        return new ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_customer, viewGroup, false));
//    }



    @Override
    protected void convert(@NotNull BaseViewHolder viewHolder, Customer item) {
        if (!TextUtils.isEmpty(item.getAvatarImagePath())) {
            File file = new File(item.getAvatarImagePath());
            if (file.exists()) {
                Glide.with(mContext)
                        .load(file)
                        .into((ImageView) viewHolder.getView(R.id.ivAvatar));
            } else {
                viewHolder.setImageResource(R.id.ivAvatar, R.drawable.ic_person_blue_24dp);
            }
        }
        viewHolder.setText(R.id.tvNickName, item.getNickName());
        viewHolder.setText(R.id.tvName, item.getName());
        viewHolder.setText(R.id.tvPhone, item.getPhoneNumber());

//        ((ViewHolder) viewHolder).ivMore.setTag(item);
//        ((ViewHolder) viewHolder).ivMore.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                if (mOnClickListener != null) {
//                    mOnClickListener.onClick(view);
//                }
//            }
//        });
//
//        ((ViewHolder) viewHolder).itemView.setTag(item);
//        ((ViewHolder) viewHolder).itemView.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//
//                if (mOnItemClickListener != null) {
//                    mOnItemClickListener.onClick(view);
//                }
//            }
//        });
    }



}
