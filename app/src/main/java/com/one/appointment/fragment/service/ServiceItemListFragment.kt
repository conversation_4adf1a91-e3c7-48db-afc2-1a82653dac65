package com.one.appointment.fragment.service

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.github.clans.fab.FloatingActionButton
import com.one.appointment.ObjectBox
import com.one.appointment.R
import com.one.appointment.activity.ToolbarActivity.Companion.toActivity
import com.one.appointment.constant.EntryPoint
import com.one.appointment.constant.KeyDefine
import com.one.appointment.databinding.FragmentServiceItemListBinding
import com.one.appointment.entity.ServiceItem
import com.one.appointment.entity.ServiceItem_
import com.one.appointment.fragment.BaseFragment
import com.one.appointment.fragment.service.adapter.ServiceItemAdapter
import com.one.core.ad.FacebookAdHelp
import com.one.core.ad.GoogleAdHelp
import io.objectbox.Box

/**
 * ServiceItemListFragment
 */
class ServiceItemListFragment : BaseFragment() {
    private var mRecyclerView: RecyclerView? = null
    private var mSwipeRefreshLayout: SwipeRefreshLayout? = null
    private var mServiceItemBox: Box<ServiceItem>? = ObjectBox.get().boxFor(ServiceItem::class.java)
    private var mAdapter: ServiceItemAdapter? = null
    private lateinit var binding: FragmentServiceItemListBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
//        return inflater.inflate(R.layout.fragment_service_item_list, container, false)
        binding = FragmentServiceItemListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        appCompatActivity.title = getString(R.string.service_item)
        if (appCompatActivity.supportActionBar != null) {
            appCompatActivity.supportActionBar!!.show()
        }
        val fab = view.findViewById<FloatingActionButton>(R.id.fabAdd)
        fab.setOnClickListener {
            val bundle = Bundle()
            //                bundle.putString(KeyDefine.NICK_NAME, message.getTitle());
            toActivity(appCompatActivity, EntryPoint.AddServiceFragment, bundle)
        }

        binding.layoutEmpty.tvEmptyDesc.text = "尚未新增服務項目"
        mRecyclerView = view.findViewById(R.id.recyclerView)
        mSwipeRefreshLayout = view.findViewById(R.id.swipeRefreshLayout)
        initRecycleView()
        initAd()

        // 初始化完成後載入數據
        initData()
    }

    override fun initView(view: View) {}

    override fun onResume() {
        super.onResume()
        // 載入數據並停止刷新動畫
        if (mSwipeRefreshLayout != null && mAdapter != null) {
            mSwipeRefreshLayout!!.isRefreshing = true
            initData()
        }
    }

    private fun initData() {
        if (mAdapter != null) {
            mAdapter!!.clearList()
            data
        } else {
            // 如果 adapter 還沒初始化，直接停止刷新動畫
            mSwipeRefreshLayout?.isRefreshing = false
        }
    }

    private fun initAd() {
        GoogleAdHelp.addView(
            requireContext(),
            "ca-app-pub-1800606262336792/5337776640",
            binding.bannerContainer
        )
        FacebookAdHelp.addView(
            requireContext(),
            "521662492234245_758316028568889",
            binding.bannerContainer
        )
    }

    private val data: Unit
        get() {
            try {
                val list = mServiceItemBox?.query()?.orderDesc(ServiceItem_.id)?.build()?.find() ?: emptyList()
                if (list.isEmpty()) {
                    binding.layoutEmpty.tvEmptyDesc.visibility = View.VISIBLE
                } else {
                    binding.layoutEmpty.tvEmptyDesc.visibility = View.GONE
                }
                mAdapter?.addList(list)
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                // 確保總是停止刷新動畫
                mSwipeRefreshLayout?.isRefreshing = false
            }
        }

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(activity)
        layoutManager.orientation = RecyclerView.VERTICAL
        mRecyclerView!!.layoutManager = layoutManager
        val mServiceItemBox = ObjectBox.get().boxFor(ServiceItem::class.java)
        mAdapter = ServiceItemAdapter(activity, ArrayList())
        mAdapter!!.setMoreOnClick { view ->
            val serviceItem = view.tag as ServiceItem
            mServiceItemBox.remove(serviceItem)
            initData()
        }
        mAdapter!!.setItemOnClick { view ->
            val serviceItem = view.tag as ServiceItem
            val bundle = Bundle()
            bundle.putLong(KeyDefine.ID, serviceItem.getId())
            toActivity(appCompatActivity, EntryPoint.AddServiceFragment, bundle)
        }
        mRecyclerView!!.adapter = mAdapter

        // 設定下拉刷新
        mSwipeRefreshLayout?.setOnRefreshListener {
            //refresh data here
            initData()
        }
    }
}