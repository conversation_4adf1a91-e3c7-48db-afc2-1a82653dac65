package com.one.appointment.fragment.customer.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.one.appointment.R;
import com.one.appointment.entity.Customer;

import java.io.File;
import java.util.List;

public class CustomerItemAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final Context mContext;
    private List<Customer> mList;
    private View.OnClickListener mOnClickListener;
    private View.OnClickListener mOnItemClickListener;

    public CustomerItemAdapter(Context context, List<Customer> messageList) {
        mContext = context;
        mList = messageList;
    }

    public void setItemOnClick(View.OnClickListener onClickListener) {
        mOnItemClickListener = onClickListener;
    }

    public void setMoreOnClick(View.OnClickListener onClickListener) {
        mOnClickListener = onClickListener;
    }

    public void setList(List<Customer> houseList) {
        mList = houseList;
        notifyDataSetChanged();
    }

    public void clearList() {
        mList.clear();
        notifyDataSetChanged();
    }

    public void addList(List<Customer> list) {
        if (list == null) {
            return;
        }
        mList.addAll(list);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        return new ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_customer, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        final Customer item = mList.get(position);
        if (!TextUtils.isEmpty(item.getAvatarImagePath())) {
            File file = new File(item.getAvatarImagePath());
            if (file.exists()) {
                Glide.with(mContext)
                        .load(file) // Uri of the picture
                        .into(((ViewHolder) viewHolder).ivAvatar);
            } else {
                ((ViewHolder) viewHolder).ivAvatar.setImageResource(R.drawable.ic_person_blue_24dp);
            }
        }

        ((ViewHolder) viewHolder).tvNickName.setText(item.getNickName());
        ((ViewHolder) viewHolder).tvName.setText(item.getName());
        ((ViewHolder) viewHolder).tvPhone.setText(item.getPhoneNumber());
        ((ViewHolder) viewHolder).ivMore.setTag(item);
        ((ViewHolder) viewHolder).ivMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnClickListener != null) {
                    mOnClickListener.onClick(view);
                }
            }
        });

        ((ViewHolder) viewHolder).itemView.setTag(item);
        ((ViewHolder) viewHolder).itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onClick(view);
                }
            }
        });
    }


    @Override
    public int getItemCount() {
        if (mList == null) {
            return 0;
        }
        return mList.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivAvatar;
        private TextView tvNickName;
        private TextView tvName;
        private TextView tvPhone;
        private ImageView ivMore;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivAvatar = itemView.findViewById(R.id.ivAvatar);
            tvNickName = itemView.findViewById(R.id.tvNickName);
            tvName = itemView.findViewById(R.id.tvName);
            tvPhone = itemView.findViewById(R.id.tvPhone);
            ivMore = itemView.findViewById(R.id.ivMore);

        }

    }


}
