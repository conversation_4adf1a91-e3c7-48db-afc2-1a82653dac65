package com.one.appointment.fragment.appointment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.one.appointment.ObjectBox;
import com.one.appointment.R;
import com.one.appointment.entity.Customer;
import com.one.appointment.entity.Customer_;
import com.one.appointment.fragment.customer.adapter.CustomerItemQuickAdapter;

import java.util.ArrayList;
import java.util.List;

import io.objectbox.Box;

public class CustomerDialogFragment extends DialogFragment {


    private final View.OnClickListener mOnClickListener;
    private RecyclerView mRecyclerView;
    private Box<Customer> mCustomerBox;
    private CustomerItemQuickAdapter mAdapter;

    CustomerDialogFragment(View.OnClickListener onClickListener) {
        // Required empty public constructor
        mOnClickListener = onClickListener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        setStyle(CustomerDialogFragment.STYLE_NO_FRAME, R.style.Theme_No_Frame);
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View rootView = inflater.inflate(R.layout.dialog_fragment_customer, container, false);

        mRecyclerView = rootView.findViewById(R.id.recyclerView);

        initRecycleView();
        getData();
        return rootView;
    }

    private void getData() {
        mCustomerBox = ObjectBox.get().boxFor(Customer.class);
        List<Customer> list = mCustomerBox.query().orderDesc(Customer_.id).build().find();
        mAdapter.addList(list);
//        mRecyclerView.loadMoreComplete();
//        mRecyclerView.refreshComplete();
    }

    private void initRecycleView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        layoutManager.setOrientation(RecyclerView.VERTICAL);
        mRecyclerView.setLayoutManager(layoutManager);
        mAdapter = new CustomerItemQuickAdapter(getActivity(), new ArrayList<>());
        mAdapter.setUseEmpty(true);
        View layoutEmpty = LayoutInflater.from(getActivity()).inflate(R.layout.layout_empty, null, false);
        mAdapter.setEmptyView(layoutEmpty);

        mAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter adapter, @NonNull View view, int position) {
                if (mOnClickListener != null) {
                    Customer customer = mAdapter.getData().get(position);
                    view.setTag(customer);
                    mOnClickListener.onClick(view);
                    dismiss();
                }
            }
        });

        mRecyclerView.setAdapter(mAdapter);

//        mRecyclerView.setPullRefreshEnabled(false);
//        mRecyclerView.setRefreshHeader(new CustomRefreshHeader(getActivity()));
//        mRecyclerView.setLoadingMoreEnabled(false);
//        mRecyclerView.setLoadingMoreFooter(new CustomMoreFooter(getActivity()));
    }
}
