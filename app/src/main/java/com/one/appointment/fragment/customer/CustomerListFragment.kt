package com.one.appointment.fragment.customer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.github.clans.fab.FloatingActionButton
import com.one.appointment.ObjectBox
import com.one.appointment.R
import com.one.appointment.activity.ToolbarActivity.Companion.toActivity
import com.one.appointment.constant.EntryPoint
import com.one.appointment.constant.KeyDefine
import com.one.appointment.databinding.FragmentCustomerListBinding
import com.one.appointment.entity.Customer
import com.one.appointment.entity.Customer_
import com.one.appointment.fragment.BaseFragment
import com.one.appointment.fragment.customer.adapter.CustomerItemQuickAdapter
import com.one.core.ad.FacebookAdHelp
import com.one.core.ad.GoogleAdHelp
import io.objectbox.Box

/**
 * CustomerListFragment
 */
class CustomerListFragment : BaseFragment() {
    private var mRecyclerView: RecyclerView? = null
    private var mCustomerBox: Box<Customer>? = null
    private var mAdapter: CustomerItemQuickAdapter? = null
    private lateinit var binding: FragmentCustomerListBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
//        return inflater.inflate(R.layout.fragment_customer_list, container, false)
        binding = FragmentCustomerListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        appCompatActivity.title = getString(R.string.customer)
        val fab = view.findViewById<FloatingActionButton>(R.id.fabAdd)
        fab.setOnClickListener {
            val bundle = Bundle()
            toActivity(appCompatActivity, EntryPoint.AddCustomerFragment, bundle)
        }
        if (appCompatActivity.supportActionBar != null) {
            appCompatActivity.supportActionBar!!.show()
        }
        mRecyclerView = view.findViewById(R.id.recyclerView)
        initRecycleView()
        initAd()
    }

    override fun initView(view: View) {}

    override fun onResume() {
        super.onResume()
        initData()

    }

    private fun initData() {
        mAdapter!!.clearList()
        data
    }//        mRecyclerView.loadMoreComplete();

    //        mRecyclerView.refreshComplete();

    private fun initAd() {
        GoogleAdHelp.addView(
            requireContext(),
            "ca-app-pub-1800606262336792/5337776640",
            binding.bannerContainer
        )
        FacebookAdHelp.addView(
            requireContext(),
            "521662492234245_758316028568889",
            binding.bannerContainer
        )
    }

    val data: Unit
        get() {
            val messageList = mCustomerBox!!.query().orderDesc(Customer_.id).build().find()
            mAdapter!!.addList(messageList)
            //        mRecyclerView.loadMoreComplete();
//        mRecyclerView.refreshComplete();
        }

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(activity)
        layoutManager.orientation = RecyclerView.VERTICAL
        mRecyclerView!!.layoutManager = layoutManager
        mCustomerBox = ObjectBox.get().boxFor(Customer::class.java)
        mAdapter = CustomerItemQuickAdapter(activity, ArrayList())
        mAdapter!!.isUseEmpty = true
        val layoutEmpty = LayoutInflater.from(activity).inflate(R.layout.layout_empty, null, false)
        val tvEmptyDesc = layoutEmpty.findViewById<TextView>(R.id.tvEmptyDesc)
        tvEmptyDesc.text = "尚未新增顧客資料"
        mAdapter!!.setEmptyView(layoutEmpty)
        mAdapter!!.setOnItemClickListener { adapter, view, position ->
            val customer = mAdapter!!.data[position]
            val bundle = Bundle()
            bundle.putLong(KeyDefine.CUSTOMER_ID, customer.getId())
            toActivity(appCompatActivity, EntryPoint.AddCustomerFragment, bundle)
        }
        mAdapter!!.addChildClickViewIds(R.id.ivMore)
        mAdapter!!.setOnItemChildClickListener { adapter, view, position ->
            if (view.id == R.id.ivMore) {
                view.tag = mAdapter!!.data[position]
                showPopupMenu(view)
            }
        }
        mRecyclerView!!.adapter = mAdapter
    }

    private fun showPopupMenu(view: View) {
        val customer = view.tag as Customer
        val popupMenu = PopupMenu(context, view)
        popupMenu.menuInflater.inflate(R.menu.more_menu, popupMenu.menu)
        popupMenu.show()
        popupMenu.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.edit -> {}
                R.id.delete -> {
                    mCustomerBox!!.remove(customer)
                    initData()
                }
                R.id.addAppointment -> {
                    val bundle = Bundle()
                    bundle.putSerializable(KeyDefine.CUSTOMER_ID, customer.getId())
                    toActivity(appCompatActivity, EntryPoint.AddAppointmentFragment, bundle)
                }
                else -> {}
            }
            true
        }
        popupMenu.setOnDismissListener {
            // 控件消失时的事件
        }
        popupMenu.show()
    }
}