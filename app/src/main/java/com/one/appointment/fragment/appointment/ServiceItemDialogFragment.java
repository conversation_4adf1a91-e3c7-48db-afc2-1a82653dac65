package com.one.appointment.fragment.appointment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.one.appointment.ObjectBox;
import com.one.appointment.R;
import com.one.appointment.entity.ServiceItem;
import com.one.appointment.entity.ServiceItem_;
import com.one.appointment.fragment.service.adapter.ServiceItemAdapter;

import java.util.ArrayList;
import java.util.List;

import io.objectbox.Box;

public class ServiceItemDialogFragment extends DialogFragment {


    private final View.OnClickListener mOnClickListener;
    private RecyclerView mRecyclerView;
    private SwipeRefreshLayout mSwipeRefreshLayout;
    private Box<ServiceItem> mServiceItemBox;
    private ServiceItemAdapter mAdapter;

    ServiceItemDialogFragment(View.OnClickListener onClickListener) {
        // Required empty public constructor
        mOnClickListener = onClickListener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        setStyle(ServiceItemDialogFragment.STYLE_NO_FRAME, R.style.Theme_No_Frame);
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View rootView = inflater.inflate(R.layout.dialog_fragment_service_item, container, false);

        mRecyclerView = rootView.findViewById(R.id.recyclerView);
        mSwipeRefreshLayout = rootView.findViewById(R.id.swipeRefreshLayout);

        initRecycleView();
        getData();
        return rootView;
    }

    private void getData() {
        mServiceItemBox = ObjectBox.get().boxFor(ServiceItem.class);
        List<ServiceItem> list = mServiceItemBox.query().orderDesc(ServiceItem_.id).build().find();
        mAdapter.addList(list);
        // 對話框中的列表通常不需要刷新動畫
    }

    private void initRecycleView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        layoutManager.setOrientation(RecyclerView.VERTICAL);
        mRecyclerView.setLayoutManager(layoutManager);
        mAdapter = new ServiceItemAdapter(getActivity(), new ArrayList<>());
        mAdapter.setItemOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnClickListener != null) {
                    mOnClickListener.onClick(view);
                    dismiss();
                }
            }
        });
        mRecyclerView.setAdapter(mAdapter);

        // 對話框中的列表通常不需要下拉刷新功能
        // 如果需要，可以添加 SwipeRefreshLayout 的設定
    }
}
