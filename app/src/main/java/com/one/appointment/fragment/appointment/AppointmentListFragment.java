package com.one.appointment.fragment.appointment;


import static java.util.Collections.emptyList;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.compose.ui.platform.ComposeView;
import androidx.compose.ui.platform.ViewCompositionStrategy;
import androidx.fragment.app.Fragment;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.one.appointment.ObjectBox;
import com.one.appointment.R;
import com.one.appointment.activity.MainActivity;
import com.one.appointment.activity.ToolbarActivity;
import com.one.appointment.constant.EntryPoint;
import com.one.appointment.constant.KeyDefine;
import com.one.appointment.dialog.ModernYearMonthPickerDialog;
import com.one.appointment.entity.Appointment;
import com.one.appointment.entity.Appointment_;
import com.one.appointment.entity.Customer;
import com.one.appointment.fragment.BaseFragment;
import com.one.appointment.fragment.appointment.adapter.AppointmentItemQuickAdapter;
import com.one.appointment.util.FormatUtils;
import com.one.appointment.util.LogUtil;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import io.objectbox.Box;

/**
 * A simple {@link Fragment} subclass.
 */
public class AppointmentListFragment extends BaseFragment implements
        View.OnClickListener {

    private RecyclerView mRecyclerView;
    private Box<Appointment> mAppointmentBox;
    private AppointmentItemQuickAdapter mAdapter;

    TextView mTextMonthDay;
    TextView mTextYear;
    TextView mTextLunar;
    //    TextView mTextCurrentDay;
    ComposeView composeCalendarView;
    com.google.android.material.chip.Chip chipAppointmentCount;

    // 舊版 View Calendar 元素已移除，現在使用 Compose Calendar
    RelativeLayout mRelativeTool;
    private int mYear;
    private int mMonth;
    private int mDay;
    private com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton fabAdd;
    private MainActivity mActivity;
    private View layoutEmpty;

    // 舊版 CalendarManager 已移除，現在使用 Compose Calendar

    // Compose 日曆狀態（為未來遷移準備）
    private java.time.LocalDate selectedDate = java.time.LocalDate.now();
    private java.util.Set<java.time.LocalDate> appointmentDates = new java.util.HashSet<>();
    private java.util.List<Appointment> currentDayAppointments = new java.util.ArrayList<>();

    // 狀態更新標記
    private boolean needsComposeUpdate = false;

    // 現代化年月選擇器
    private ModernYearMonthPickerDialog yearMonthPickerDialog;

    // Compose 不需要 ViewContainer 類別

    public AppointmentListFragment() {
        // Required empty public constructor
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof MainActivity) {
            mActivity = (MainActivity) context;
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_appointment_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        getAppCompatActivity().setTitle(getString(R.string.appointment));
        fabAdd = view.findViewById(R.id.fabAdd);
        fabAdd.setOnClickListener(v -> {
            Bundle bundle = new Bundle();
            ToolbarActivity.toActivity(getAppCompatActivity(), EntryPoint.AddAppointmentFragment, bundle);
            Navigation.createNavigateOnClickListener(R.id.addAppointmentFragment, null);

//                Navigation.findNavController(v).navigate(R.id.addAppointmentFragment);
        });

        // 設定按鈕已移到新的 UI 設計中
        initRecycleView(view);
        initView(view);
        // 實現日曆資料初始化
        initCalendarData();
    }

    @SuppressLint("SetTextI18n")
    public void initView(View view) {
        layoutEmpty = LayoutInflater.from(getAppCompatActivity()).inflate(R.layout.layout_empty, null, false);
        TextView tvEmptyDesc = layoutEmpty.findViewById(R.id.tvEmptyDesc);
        tvEmptyDesc.setText("今日尚無預約");

        mTextMonthDay = view.findViewById(R.id.tv_month_day);
        mTextYear = view.findViewById(R.id.tv_year);
        mTextLunar = view.findViewById(R.id.tv_lunar);
        composeCalendarView = view.findViewById(R.id.composeCalendarView);

        // 初始化預約數量顯示 Chip
        chipAppointmentCount = view.findViewById(R.id.chipAppointmentCount);

        // 舊版 View Calendar 元素已移除

        // 舊版 CalendarManager 和 View Calendar 已移除

        // 初始化 Compose 日曆
        setupComposeCalendar(null);

        // 初始化現代化年月選擇器
        yearMonthPickerDialog = new ModernYearMonthPickerDialog(getContext());

        // 設定點擊事件
        mTextMonthDay.setOnClickListener(v -> {
            // 顯示年月選擇對話框
            showYearMonthPicker();
        });

        // 舊的 CalendarManager 點擊事件已移除，現在使用 Compose Calendar

        // 舊的設定按鈕點擊事件已移除，使用新的今天按鈕

        // 初始化當前日期顯示
        java.time.LocalDate today = java.time.LocalDate.now();
        updateDateDisplay(today);
    }

    /**
     * 設定 Compose 日曆
     */
    private void setupComposeCalendar(List<Appointment> appointments) {
        if (composeCalendarView == null) {
            return;
        }

        // 設定 Compose 策略
        composeCalendarView.setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed.INSTANCE
        );

        try {
            // 使用完整的生產版本 Compose Calendar
            com.one.appointment.compose.CalendarBridge.INSTANCE.setupProductionCalendarContent(
                composeCalendarView,
                selectedDate,
                appointmentDates,
                currentDayAppointments,
                    date -> {
                        AppointmentListFragment.this.onDateSelected(date);
                        // 同時更新 View Calendar 顯示
                        AppointmentListFragment.this.updateViewCalendarDisplay();
                    },
                    AppointmentListFragment.this::onViewModeChanged,
                    AppointmentListFragment.this::onAddAppointment,
                    AppointmentListFragment.this::onAppointmentDelete,
                    AppointmentListFragment.this::onAppointmentClick
            );
            LogUtil.d("🎨 完整版 Compose Calendar 設置成功");
        } catch (Exception e) {
            LogUtil.e("完整版 Calendar 設置失敗，使用功能測試版本: " + e.getMessage());
            // 如果完整版本失敗，降級到功能測試版本
            try {
                com.one.appointment.compose.CalendarBridge.INSTANCE.setupFunctionTestContent(
                    composeCalendarView,
                    selectedDate,
                    appointmentDates,
                        AppointmentListFragment.this::onDateSelected,
                        AppointmentListFragment.this::onViewModeChanged,
                        AppointmentListFragment.this::onAddAppointment
                );
                LogUtil.d("🧪 功能測試版本 Calendar 設置成功");
            } catch (Exception e2) {
                LogUtil.e("所有版本都失敗，使用基本測試內容: " + e2.getMessage());
            }
        }

        // 先添加測試預約數據
        addTestAppointmentData();

        // 載入預約事件（如果 ObjectBox 已初始化）
        loadAppointmentEvents();
    }



    /**
     * 更新預約數量顯示
     */
    private void updateAppointmentCount(int count) {
        if (chipAppointmentCount != null) {
            chipAppointmentCount.setText(String.valueOf(count));
            LogUtil.d("✅ 預約數量已更新: " + count);
        } else {
            LogUtil.e("chipAppointmentCount 尚未初始化");
        }
    }

    // setupViewCalendarForComparison 方法已移除

    /**
     * 更新頂部日期顯示
     */
    private void updateTopDateDisplay(java.time.LocalDate date) {
        try {
            // 更新年份顯示
            if (mTextYear != null) {
                mTextYear.setText(String.valueOf(date.getYear()));
            }

            // 更新月日顯示
            if (mTextMonthDay != null) {
                @SuppressLint("DefaultLocale") String monthDay = String.format("%d月%d日", date.getMonthValue(), date.getDayOfMonth());
                mTextMonthDay.setText(monthDay);
            }

            // 更新農曆顯示（如果需要）
            if (mTextLunar != null) {
                // 這裡可以添加農曆轉換邏輯
                String dayOfWeek = getDayOfWeekString(date);
                mTextLunar.setText(dayOfWeek);
            }

            LogUtil.d("✅ 頂部日期顯示已更新: " + date.toString());

        } catch (Exception e) {
            LogUtil.e("更新頂部日期顯示失敗: " + e.getMessage());
        }
    }

    /**
     * 獲取星期字串
     */
    private String getDayOfWeekString(java.time.LocalDate date) {
        java.time.DayOfWeek dayOfWeek = date.getDayOfWeek();
        switch (dayOfWeek) {
            case MONDAY: return "星期一";
            case TUESDAY: return "星期二";
            case WEDNESDAY: return "星期三";
            case THURSDAY: return "星期四";
            case FRIDAY: return "星期五";
            case SATURDAY: return "星期六";
            case SUNDAY: return "星期日";
            default: return "";
        }
    }

    /**
     * 更新 Compose Calendar 顯示
     */
    private void updateComposeCalendar(List<Appointment> appointments) {
        if (composeCalendarView == null) {
            LogUtil.d("ComposeView 不可用，跳過更新");
            return;
        }

        LogUtil.d("🔄 更新 Compose Calendar - 選中日期: " + selectedDate.toString() + ", 預約數量: " + appointmentDates.size());

        try {
            // 重新設置 Compose Calendar 內容以反映最新狀態
            com.one.appointment.compose.CalendarBridge.INSTANCE.setupProductionCalendarContent(
                composeCalendarView,
                selectedDate,
                appointmentDates,
                currentDayAppointments,
                    AppointmentListFragment.this::onDateSelected,
                    AppointmentListFragment.this::onViewModeChanged,
                    AppointmentListFragment.this::onAddAppointment,
                    AppointmentListFragment.this::onAppointmentDelete,
                    AppointmentListFragment.this::onAppointmentClick
            );

            needsComposeUpdate = false;
            LogUtil.d("✅ Compose Calendar 更新成功");

        } catch (Exception e) {
            LogUtil.e("Compose Calendar 更新失敗: " + e.getMessage());
        }
    }

    /**
     * 更新 View Calendar 顯示
     */
    private void updateViewCalendarDisplay() {
        LogUtil.d("更新顯示 - 選中日期: " + selectedDate.toString());

        // 更新頂部顯示
        updateTopDateDisplay(selectedDate);

        // 更新 Compose Calendar
        updateComposeCalendar(emptyList());
    }

    /**
     * 添加測試預約數據
     */
    private void addTestAppointmentData() {
        try {
            // 避免重複添加
            if (!appointmentDates.isEmpty()) {
                LogUtil.d("測試預約數據已存在，跳過重複添加");
                return;
            }

            // 添加一些測試預約日期
            java.time.LocalDate today = java.time.LocalDate.now();
            appointmentDates.add(today);
            appointmentDates.add(today.plusDays(1));
            appointmentDates.add(today.plusDays(3));
            appointmentDates.add(today.plusDays(7));
            appointmentDates.add(today.minusDays(2));

            LogUtil.d("添加了 " + appointmentDates.size() + " 個測試預約日期");

            // 注意：不在這裡重新調用 setupComposeCalendar，避免無限循環

        } catch (Exception e) {
            LogUtil.e("添加測試預約數據失敗: " + e.getMessage());
        }
    }

    /**
     * 處理日期選擇
     */
    private void handleDateSelection(java.time.LocalDate date) {
        // 更新傳統的日期顯示
        updateDateDisplay(date);

        // 顯示相關 UI 元素
        mTextLunar.setVisibility(View.VISIBLE);
        mTextYear.setVisibility(View.VISIBLE);
        fabAdd.setVisibility(View.VISIBLE);
        mActivity.binding.bottomNavigation.setVisibility(View.VISIBLE);

        // 重新載入該日期的預約資料
        initData();

        LogUtil.d("選擇日期: " + date);
    }

    /**
     * 更新日期顯示
     */
    private void updateDateDisplay(java.time.LocalDate date) {
        mYear = date.getYear();
        mMonth = date.getMonthValue(); // LocalDate.getMonthValue() 是 1-based
        mDay = date.getDayOfMonth();

        mTextYear.setText(String.valueOf(mYear));
        mTextMonthDay.setText(mMonth + "月" + mDay + "日");
        mTextLunar.setText("今日");
        mTextLunar.setVisibility(View.VISIBLE);
    }

    // 實現日曆資料初始化功能
    private void initCalendarData() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);

        LogUtil.d(year + "/" + month + "/" + day);

        // 載入預約事件標記
        loadAppointmentEvents();
    }

    /**
     * 載入預約事件到日曆上顯示
     */
    private void loadAppointmentEvents() {
        try {
            // 檢查 ObjectBox 是否已初始化
            if (mAppointmentBox == null) {
                LogUtil.d("mAppointmentBox 尚未初始化，使用測試數據模式");
                // 在測試模式下，我們已經有測試預約數據，無需從數據庫載入
                return;
            }

            Set<java.time.LocalDate> appointmentDates = new HashSet<>();

            // 查詢所有預約資料
            List<Appointment> appointments = mAppointmentBox.getAll();

            for (Appointment appointment : appointments) {
                if (appointment.getTime() > 0) {
                    try {
                        // 將時間戳轉換為 LocalDate
                        java.time.Instant instant = java.time.Instant.ofEpochMilli(appointment.getTime());
                        java.time.LocalDate appointmentDate = instant.atZone(java.time.ZoneId.systemDefault()).toLocalDate();

                        // 添加到預約日期集合
                        appointmentDates.add(appointmentDate);
                    } catch (Exception e) {
                        LogUtil.e("解析預約日期失敗: " + appointment.getTime());
                    }
                }
            }

            // 更新 Compose 日曆中的預約日期
            this.appointmentDates = appointmentDates;

            // 更新 Compose Calendar 顯示
            updateComposeCalendar(appointments);

            LogUtil.i("載入了 " + appointmentDates.size() + " 個預約事件到日曆");

        } catch (Exception e) {
            LogUtil.e("載入預約事件失敗: " + e.getMessage());
        }
    }


    // 日曆標記功能已整合到 markAppointmentDates() 方法中
    // MaterialCalendarView 使用不同的方式來標記日期

    @Override
    public void onResume() {
        super.onResume();
        LogUtil.d("🔄 Fragment onResume - 重新載入數據");
        initData();

        // 確保 Compose Calendar 顯示最新數據
        if (needsComposeUpdate || appointmentDates.isEmpty()) {
            LogUtil.d("🔄 onResume 觸發 Compose Calendar 更新");
            updateComposeCalendar(emptyList());
        }
    }

    private void initData() {
        mAdapter.setNewData(null);
        getData();
    }

    private void getData() {
        String year = String.valueOf(mYear);
        String month = String.valueOf(mMonth);
        String day = String.valueOf(mDay);
        // tvCurrentDay 已移除，現在使用 Compose Calendar

        Date date = FormatUtils.stringToDate(year + "/" + month + "/" + day, "yyyy/MM/dd");
        long today = date.getTime();
        long tomorrow = date.getTime() + (24 * 60 * 60 * 1000) - 1000;
        // 檢查 ObjectBox 是否已初始化
        if (mAppointmentBox == null) {
            LogUtil.e("mAppointmentBox 尚未初始化，無法載入預約資料");
            return;
        }

        List<Appointment> list = mAppointmentBox.query().between(Appointment_.time, today, tomorrow)
                .order(Appointment_.time).build().find();
        mAdapter.removeHeaderView(layoutEmpty);
        if (list.isEmpty()) {
            mAdapter.addHeaderView(layoutEmpty);
        }
        mAdapter.setNewData(list);

        // 更新當天預約數據供 Compose 使用
        currentDayAppointments.clear();
        currentDayAppointments.addAll(list);

        LogUtil.d("📋 載入了 " + list.size() + " 個預約，日期: " + year + "/" + month + "/" + day);
        LogUtil.d("📋 currentDayAppointments 大小: " + currentDayAppointments.size());

        // 更新預約數量顯示
        updateAppointmentCount(list.size());

        // 重新載入所有預約事件以更新 Compose Calendar
        loadAppointmentEvents();

        // 更新 Compose Calendar 以顯示當天預約
        updateComposeCalendar(list);
    }

    private void initRecycleView(View view) {
        mRecyclerView = view.findViewById(R.id.recyclerView);
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        layoutManager.setOrientation(RecyclerView.VERTICAL);
        mRecyclerView.setLayoutManager(layoutManager);
        mAppointmentBox = ObjectBox.get().boxFor(Appointment.class);
        mAdapter = new AppointmentItemQuickAdapter(getAppCompatActivity(), new ArrayList<>());

        mAdapter.setOnItemClickListener((adapter, view1, position) -> {
            final Appointment appointment = mAdapter.getData().get(position);
            Bundle bundle = new Bundle();
            bundle.putLong(KeyDefine.ID, appointment.id);
            ToolbarActivity.toActivity(getAppCompatActivity(), EntryPoint.AddAppointmentFragment, bundle);
        });

        // 先注册需要点击的子控件id（注意，请不要写在convert方法里）
        mAdapter.addChildClickViewIds(R.id.ivDelete);
        // 设置子控件点击监听
        mAdapter.setOnItemChildClickListener((adapter, view12, position) -> {
            if (view12.getId() == R.id.ivDelete) {
                final Appointment appointment = mAdapter.getData().get(position);
                if (mAppointmentBox != null) {
                    mAppointmentBox.remove(appointment);
                    initData();
                } else {
                    LogUtil.e("mAppointmentBox 尚未初始化，無法刪除預約");
                }
            }
        });



        mRecyclerView.setAdapter(mAdapter);
    }

    @Override
    public void onClick(View view) {

    }

    // Kizitonwose Calendar 不需要實現特定的回調介面
    // 所有的交互都在 DayBinder 中處理

    /**
     * 顯示年月選擇對話框 - 使用現代化 View 實現
     */
    private void showYearMonthPicker() {
        if (yearMonthPickerDialog == null) {
            yearMonthPickerDialog = new ModernYearMonthPickerDialog(getContext());
        }

        // 獲取當前年月
        java.time.YearMonth currentYearMonth = java.time.YearMonth.from(selectedDate);

        // 顯示現代化年月選擇器
        yearMonthPickerDialog.show(currentYearMonth, selectedYearMonth -> {
            try {
                // 創建選擇的日期
                java.time.LocalDate newSelectedDate = selectedYearMonth.atDay(1);

                // 更新選中日期
                this.selectedDate = newSelectedDate;

                // 處理日期選擇
                handleDateSelection(newSelectedDate);

                // 重新設置 Compose 內容以更新顯示
                setupComposeCalendar(emptyList());

                // 重新載入預約事件
                loadAppointmentEvents();

                LogUtil.d("現代化年月選擇器選擇了: " + selectedYearMonth);

            } catch (Exception e) {
                LogUtil.e("年月選擇處理失敗: " + e.getMessage());
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 清理現代化年月選擇器
        if (yearMonthPickerDialog != null && yearMonthPickerDialog.isShowing()) {
            yearMonthPickerDialog.dismiss();
        }
        yearMonthPickerDialog = null;
    }

    /**
     * Compose 日曆日期選擇回調
     */
    private void onDateSelected(java.time.LocalDate date) {
        LogUtil.d("🧪 測試結果 - 日期選擇功能: 成功");
        LogUtil.d("📅 選中日期: " + date.toString());

        // 檢查是否真的需要更新（避免無限循環）
        if (selectedDate.equals(date)) {
            LogUtil.d("📅 日期未變化，跳過更新");
            return;
        }

        // 更新選中日期
        selectedDate = date;

        // 更新頂部的年月日顯示
        updateTopDateDisplay(date);

        // 處理原始的日期選擇邏輯
        handleDateSelection(date);

        LogUtil.d("✅ 日期選擇事件處理完成");
    }

    /**
     * Compose 日曆視圖模式變更回調
     */
    private void onViewModeChanged(com.one.appointment.compose.CalendarViewMode mode) {
        LogUtil.d("🧪 測試結果 - 視圖模式切換功能: 成功");
        LogUtil.d("🔄 切換到: " + mode.name() + " 視圖");
        LogUtil.d("✅ 視圖模式變更事件處理完成");
    }

    /**
     * Compose 日曆添加預約回調
     */
    private void onAddAppointment() {
        LogUtil.d("🧪 測試結果 - 添加預約功能: 成功觸發");
        LogUtil.d("📊 當前測試狀態:");
        LogUtil.d("   - 選中日期: " + selectedDate.toString());
        LogUtil.d("   - 預約數量: " + appointmentDates.size());
        LogUtil.d("   - Compose Calendar: 運行正常");

        // 跳轉到添加預約頁面
        if (mActivity != null) {
            try {
                // 創建 Bundle 並傳遞選中的日期
                Bundle bundle = new Bundle();

                // 將選中的日期轉換為時間戳傳遞給 AddAppointmentFragment
                java.time.ZoneId zoneId = java.time.ZoneId.systemDefault();
                long timestamp = selectedDate.atStartOfDay(zoneId).toInstant().toEpochMilli();
                bundle.putLong("selectedDate", timestamp);

                // 使用 ToolbarActivity 跳轉到 AddAppointmentFragment
                com.one.appointment.activity.ToolbarActivity.toActivity(
                    mActivity,
                    com.one.appointment.constant.EntryPoint.AddAppointmentFragment,
                    bundle
                );

                LogUtil.d("✅ 成功跳轉到添加預約頁面，選中日期: " + selectedDate.toString());

            } catch (Exception e) {
                LogUtil.e("跳轉到添加預約頁面失敗: " + e.getMessage());
                // 降級處理：使用原始的 FAB 按鈕邏輯
                if (fabAdd != null) {
                    fabAdd.performClick();
                }
            }
        }
    }

    /**
     * 獲取預約顯示標題
     */
    private String getAppointmentDisplayTitle(Appointment appointment) {
        try {
            Customer customer = appointment.getCustomer().getTarget();
            if (customer != null && customer.getName() != null && !customer.getName().isEmpty()) {
                return customer.getName();
            } else {
                return "預約客戶";
            }
        } catch (Exception e) {
            return "預約客戶";
        }
    }

    /**
     * Compose 日曆預約點擊回調 - 查看預約詳情
     */
    private void onAppointmentClick(Appointment appointment) {
        String displayTitle = getAppointmentDisplayTitle(appointment);
        LogUtil.d("👆 點擊預約 - ID: " + appointment.getId() + ", 標題: " + displayTitle);

        try {
            // 創建 Bundle 並傳遞預約 ID
            Bundle bundle = new Bundle();
            bundle.putLong(com.one.appointment.constant.KeyDefine.ID, appointment.getId());

            // 跳轉到 AddAppointmentFragment 查看/編輯預約
            com.one.appointment.activity.ToolbarActivity.toActivity(
                getAppCompatActivity(),
                com.one.appointment.constant.EntryPoint.AddAppointmentFragment,
                bundle
            );

            LogUtil.d("✅ 成功跳轉到預約詳情頁面");

        } catch (Exception e) {
            LogUtil.e("跳轉到預約詳情頁面失敗: " + e.getMessage());
            if (getContext() != null) {
                android.widget.Toast.makeText(getContext(), "無法打開預約詳情", android.widget.Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * Compose 日曆刪除預約回調
     */
    private void onAppointmentDelete(Appointment appointment) {
        String displayTitle = getAppointmentDisplayTitle(appointment);
        LogUtil.d("🗑️ 刪除預約請求 - ID: " + appointment.getId() + ", 標題: " + displayTitle);

        try {
            // 顯示確認對話框
            new androidx.appcompat.app.AlertDialog.Builder(requireContext())
                .setTitle("確認刪除")
                .setMessage("確定要刪除預約「" + displayTitle + "」嗎？")
                .setPositiveButton("刪除", (dialog, which) -> {
                    try {
                        // 從數據庫刪除預約
                        mAppointmentBox.remove(appointment);
                        LogUtil.d("✅ 預約刪除成功 - ID: " + appointment.getId());

                        // 重新載入數據
                        getData();

                        // 顯示成功提示
                        if (getContext() != null) {
                            android.widget.Toast.makeText(getContext(), "預約已刪除", android.widget.Toast.LENGTH_SHORT).show();
                        }

                    } catch (Exception e) {
                        LogUtil.e("刪除預約失敗: " + e.getMessage());
                        if (getContext() != null) {
                            android.widget.Toast.makeText(getContext(), "刪除失敗: " + e.getMessage(), android.widget.Toast.LENGTH_SHORT).show();
                        }
                    }
                })
                .setNegativeButton("取消", null)
                .show();

        } catch (Exception e) {
            LogUtil.e("顯示刪除確認對話框失敗: " + e.getMessage());
        }
    }

}