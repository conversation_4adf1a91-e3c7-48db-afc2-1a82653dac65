package com.one.appointment.fragment.message.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.one.appointment.R;
import com.one.appointment.entity.Message;
import com.one.appointment.util.FormatUtils;
import com.one.appointment.util.ImageUtil;

import java.io.File;
import java.util.List;

public class MessageAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final Context mContext;
    private final View.OnClickListener mOnClickListener;
    private List<Message> mList;

    public MessageAdapter(Context context, List<Message> messageList, View.OnClickListener onClickListener) {
        mContext = context;
        mList = messageList;
        mOnClickListener = onClickListener;
    }

    public void setList(List<Message> houseList) {
        mList = houseList;
        notifyDataSetChanged();
    }

    public void clearList() {
        mList.clear();
        notifyDataSetChanged();
    }

    public void addList(List<Message> list) {
        if (list == null) {
            return;
        }
        mList.addAll(list);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        return new ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_message, viewGroup, false));
    }


    @SuppressLint("SetTextI18n")
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        final Message message = mList.get(position);
        File file = ImageUtil.getImageFile(mContext, message.getPackageName(), message.getTitle());
        if (file.exists()) {
            Glide.with(mContext)
                    .load(file) // Uri of the picture
                    .into(((ViewHolder) viewHolder).imageView);
        } else {
            ((ViewHolder) viewHolder).imageView.setImageResource(R.drawable.ic_person_blue_24dp);
        }

        ((ViewHolder) viewHolder).tvTitle.setText(message.getTitle());
        ((ViewHolder) viewHolder).tvContent.setText(message.getContent());
        ((ViewHolder) viewHolder).tvPostTime.setText((FormatUtils.longToString(message.getPostTime(), "MM/dd HH:mm")));
        ((ViewHolder) viewHolder).ivMore.setTag(message);
        ((ViewHolder) viewHolder).ivMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnClickListener != null) {
                    mOnClickListener.onClick(view);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        if (mList == null) {
            return 0;
        }
        return mList.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private TextView tvTitle;
        private TextView tvContent;
        private TextView tvPostTime;
        private ImageView ivMore;
        private ImageView imageView;


        ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.tvTitle);
            tvContent = itemView.findViewById(R.id.tvContent);
            tvPostTime = itemView.findViewById(R.id.tvPostTime);
            ivMore = itemView.findViewById(R.id.ivMore);
            imageView = itemView.findViewById(R.id.imageView);
        }

    }


}
