package com.one.appointment.fragment.customer;


import android.app.DatePickerDialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.google.android.material.snackbar.Snackbar;
import com.one.appointment.ObjectBox;
import com.one.appointment.R;
import com.one.appointment.activity.ToolbarActivity;
import com.one.appointment.constant.EntryPoint;
import com.one.appointment.constant.KeyDefine;
import com.one.appointment.entity.Customer;
import com.one.appointment.entity.Customer_;
import com.one.appointment.fragment.BaseFragment;
import com.one.appointment.util.FormatUtils;
import com.one.appointment.util.InputTools;

import java.io.File;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.TimeZone;

import io.objectbox.Box;

/**
 * AddCustomerFragment
 */
public class AddCustomerFragment extends BaseFragment implements View.OnClickListener {

    private EditText etNickName;
    private EditText etName;
    private EditText etPhone;
    private TextView tvBirthday;
    private Button btAdd;
    private Customer mCustomer;
    private String imagePath;
    private Box<Customer> mCustomerBox;
    private View btAppointmentRecord;
    private ImageView ivAvatar;


    public AddCustomerFragment() {
        // Required empty public constructor
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_add_customer, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mCustomerBox = ObjectBox.get().boxFor(Customer.class);
        ivAvatar = view.findViewById(R.id.ivAvatar);
        etNickName = view.findViewById(R.id.etNickName);
        etName = view.findViewById(R.id.etName);
        etPhone = view.findViewById(R.id.etPhone);
        tvBirthday = view.findViewById(R.id.tvBirthday);
        btAdd = view.findViewById(R.id.btAdd);
        btAppointmentRecord = view.findViewById(R.id.btAppointmentRecord);

        mCustomer = new Customer();

        if (getArguments() != null) {
            long customerId = getArguments().getLong(KeyDefine.CUSTOMER_ID, -1);

            if (customerId != -1) {
                mCustomer = mCustomerBox.query().equal(Customer_.id, customerId).build().findFirst();
                if (mCustomer != null) {
                    if (!TextUtils.isEmpty(mCustomer.getAvatarImagePath())) {
                        File file = new File(mCustomer.getAvatarImagePath());
                        if (file.exists()) {
                            Glide.with(getAppCompatActivity())
                                    .load(file) // Uri of the picture
                                    .into(ivAvatar);
                        } else {
                            ivAvatar.setImageResource(R.drawable.ic_person_blue_24dp);
                        }
                    }
                    etNickName.setText(mCustomer.getNickName());
                    etName.setText(mCustomer.getName());
                    etPhone.setText(mCustomer.getPhoneNumber());
                    tvBirthday.setText(mCustomer.getBirthday());
                    btAppointmentRecord.setVisibility(View.VISIBLE);
                    btAppointmentRecord.setOnClickListener(this);
                }
                btAdd.setText("儲存");
            } else {
                btAppointmentRecord.setVisibility(View.GONE);
            }

            String nickName = getArguments().getString(KeyDefine.NICK_NAME, "");
            imagePath = getArguments().getString(KeyDefine.IMAGE_PATH, "");
            if (!TextUtils.isEmpty(imagePath)) {
                mCustomer.setAvatarImagePath(imagePath);
            }
            if (!TextUtils.isEmpty(nickName)) {
                etNickName.setText(nickName);
            }
        }

        tvBirthday.setOnClickListener(this);
        btAdd.setOnClickListener(this);
    }

    @Override
    public void initView(View view) {

    }

    private void datePicker(TextView textView) {
        Calendar calendar = Calendar.getInstance();
        TimeZone timeZone = TimeZone.getTimeZone("GMT+8");
        calendar.setTimeZone(timeZone);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DAY_OF_MONTH);

        DatePickerDialog datePickerDialog = new DatePickerDialog(textView.getContext(), new DatePickerDialog.OnDateSetListener() {
            @Override
            public void onDateSet(DatePicker view, int year, int month, int day) {
                Calendar calendar = new GregorianCalendar(year, month, day);
                TimeZone timeZone = TimeZone.getTimeZone("GMT+8");
                calendar.setTimeZone(timeZone);
                textView.setText(FormatUtils.longToString(calendar.getTimeInMillis(), "yyyy/MM/dd"));
            }
        }, year, month, day);
        datePickerDialog.show();
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.tvBirthday) {
            datePicker(tvBirthday);
        } else if (id == R.id.btAdd) {
            if (TextUtils.isEmpty(etNickName.getText().toString())) {
                InputTools.HideKeyboard(btAdd);
                Snackbar.make(btAdd, getString(R.string.please_fill_in_the_information), Snackbar.LENGTH_LONG).show();
                return;
            }
            mCustomer.setNickName(etNickName.getText().toString());
            mCustomer.setName(etName.getText().toString());
            mCustomer.setPhoneNumber(etPhone.getText().toString());
//                mCustomer.setAvatarImagePath(imagePath);
            mCustomer.setBirthday(tvBirthday.getText().toString());
            mCustomerBox.put(mCustomer);
            getAppCompatActivity().finish();
        } else if (id == R.id.btAppointmentRecord) {
            Bundle bundle = new Bundle();
            bundle.putLong(KeyDefine.CUSTOMER_ID, mCustomer.getId());
            ToolbarActivity.toActivity(getAppCompatActivity(), EntryPoint.AppointmentRecordFragment, bundle);
        }
    }
}
