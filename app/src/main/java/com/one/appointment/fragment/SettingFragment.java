package com.one.appointment.fragment;


import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.one.appointment.R;
import com.one.appointment.activity.ToolbarActivity;
import com.one.appointment.constant.EntryPoint;

/**
 * SettingFragment
 */
public class SettingFragment extends BaseFragment implements View.OnClickListener {

    private Button btCustomer;
    private Button btServiceItem;
    private Button btSetAppointmentFormat;
    private Button btAbout;


    public SettingFragment() {
        // Required empty public constructor
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_setting, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        btCustomer = view.findViewById(R.id.btCustomer);
        btServiceItem = view.findViewById(R.id.btServiceItem);
        btSetAppointmentFormat = view.findViewById(R.id.btSetAppointmentFormat);
        btAbout = view.findViewById(R.id.btAbout);

        btCustomer.setOnClickListener(this);
        btServiceItem.setOnClickListener(this);
        btSetAppointmentFormat.setOnClickListener(this);
        btAbout.setOnClickListener(this);
    }

    @Override
    public void initView(View view) {

    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.btCustomer) {
            Bundle bundle = new Bundle();
//                bundle.putString(KeyDefine.NICK_NAME, message.getTitle());
            ToolbarActivity.toActivity(getAppCompatActivity(), EntryPoint.CustomerListFragment, bundle);
        } else if (id == R.id.btServiceItem) {
            Bundle bundle = new Bundle();
//                bundle.putString(KeyDefine.NICK_NAME, message.getTitle());
            ToolbarActivity.toActivity(getAppCompatActivity(), EntryPoint.ServiceItemFragment, bundle);
        } else if (id == R.id.btSetAppointmentFormat) {
            // TODO: 實現設定預約格式功能
        } else if (id == R.id.btAbout) {
            // TODO: 實現關於頁面功能
        }
    }
}
