package com.one.appointment.fragment.appointment.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.one.appointment.R;
import com.one.appointment.entity.Appointment;
import com.one.appointment.util.FormatUtils;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.Date;
import java.util.List;

public class AppointmentItemQuickAdapter extends BaseQuickAdapter<Appointment, BaseViewHolder> {

    private Context mContext;


    public AppointmentItemQuickAdapter(Context context, List<Appointment> list) {
        super(R.layout.item_appointment, list);
        mContext = context;
    }

    @Override
    protected void convert(@NotNull BaseViewHolder viewHolder, Appointment item) {
        if (item.getCustomer().getTarget() != null) {
            if (!TextUtils.isEmpty(item.getCustomer().getTarget().getAvatarImagePath())) {
                File file = new File(item.getCustomer().getTarget().getAvatarImagePath());
                if (file.exists()) {
                    Glide.with(mContext)
                            .load(file)
                            .into((ImageView) viewHolder.getView(R.id.ivAvatar));
                } else {
                    viewHolder.setImageResource(R.id.ivAvatar, R.drawable.ic_person_blue_24dp);
                }
            }
            if (TextUtils.isEmpty(item.getCustomer().getTarget().getName())) {
                viewHolder.getView(R.id.tvCustomerName).setVisibility(View.GONE);
            } else {
                viewHolder.getView(R.id.tvCustomerName).setVisibility(View.VISIBLE);
                viewHolder.setText(R.id.tvCustomerName, item.getCustomer().getTarget().getName());
            }
            if (TextUtils.isEmpty(item.getCustomer().getTarget().getNickName())) {
                viewHolder.getView(R.id.tvCustomerNickName).setVisibility(View.GONE);
            } else {
                viewHolder.getView(R.id.tvCustomerNickName).setVisibility(View.VISIBLE);
                viewHolder.setText(R.id.tvCustomerNickName, item.getCustomer().getTarget().getNickName());
            }
        }


        Date date = new Date(item.getTime());
        viewHolder.setText(R.id.tvTime, FormatUtils.dateToString(date, "yyyy/MM/dd HH:mm"));


        StringBuilder serviceItem = new StringBuilder();
        for (int i = 0; i < item.getServiceItemList().size(); i++) {
            serviceItem.append(item.getServiceItemList().get(i).getName()).append(" ");
        }
        if (TextUtils.isEmpty(serviceItem)) {
            viewHolder.getView(R.id.tvServiceItem).setVisibility(View.GONE);
        } else {
            viewHolder.getView(R.id.tvServiceItem).setVisibility(View.VISIBLE);
            viewHolder.setText(R.id.tvServiceItem, serviceItem);
        }

    }
}
