package com.one.appointment.fragment;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.one.appointment.R;
import com.one.appointment.util.FragmentUtils;


/**
 * BaseFragment
 */
public abstract class BaseFragment extends Fragment {


    private Context mContext;
    private AppCompatActivity mActivity;

    public BaseFragment() {
        // Required empty public constructor
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof AppCompatActivity) {
            mActivity = (AppCompatActivity) context;
        }

        mContext = context;
    }

    public Context getContext() {
        return mContext;
    }

    public AppCompatActivity getAppCompatActivity() {
        return mActivity;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initView(view);
    }

    public abstract void initView(View view);

    /**
     * [頁面跳轉]
     *
     * @param clz
     */
    public void startActivity(Class<?> clz) {
        startActivity(new Intent(mContext, clz));
    }

    /**
     * [攜帶數據的頁面跳轉]
     *
     * @param clz
     * @param bundle
     */
    public void startActivity(Class<?> clz, Bundle bundle) {
        Intent intent = new Intent();
        intent.setClass(mContext, clz);
        if (bundle != null) {
            intent.putExtras(bundle);
        }
        startActivity(intent);
    }

    public void toFragment(Class<? extends Fragment> clazz) {
        FragmentUtils.startFragment(getAppCompatActivity().getSupportFragmentManager(),
                clazz, R.id.fragmentContainer,
                null, true, FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }

    public void toFragment(Class<? extends Fragment> clazz, Bundle bundle, boolean isAddToStack) {
        FragmentUtils.startFragment(getAppCompatActivity().getSupportFragmentManager(),
                clazz, R.id.fragmentContainer,
                bundle, isAddToStack);
    }

    public void toFragment(Class<? extends Fragment> clazz, int widgetId, Bundle bundle, boolean isAddToStack) {
        FragmentUtils.startFragment(getAppCompatActivity().getSupportFragmentManager(),
                clazz, widgetId,
                bundle, isAddToStack);
    }

    protected void toFragment(Class<? extends Fragment> clazz, Bundle bundle) {
        FragmentUtils.startFragment(getAppCompatActivity().getSupportFragmentManager(),
                clazz, R.id.fragmentContainer,
                bundle, true, FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }
}
