package com.one.appointment.fragment.customer.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.one.appointment.R;
import com.one.appointment.entity.Appointment;
import com.one.appointment.entity.ServiceItem;
import com.one.appointment.util.FormatUtils;

import java.util.Date;
import java.util.List;

public class AppointmentRecordItemAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final Context mContext;
    private View.OnClickListener mOnClickListener;
    private List<Appointment> mList;
    private View.OnClickListener mMoreOnClickListener;

    public AppointmentRecordItemAdapter(Context context, List<Appointment> list) {
        mContext = context;
        mList = list;
    }

    public void setItemOnClick(View.OnClickListener onClickListener) {
        mOnClickListener = onClickListener;
    }

    public void setMoreOnClick(View.OnClickListener onClickListener) {
        mMoreOnClickListener = onClickListener;
    }

    public void setList(List<Appointment> list) {
        mList = list;
        notifyDataSetChanged();
    }

    public void clearList() {
        mList.clear();
        notifyDataSetChanged();
    }

    public void addList(List<Appointment> list) {
        if (list == null) {
            return;
        }
        mList.addAll(list);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        return new ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_appointment_record, viewGroup, false));
    }


    @SuppressLint("SetTextI18n")
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        final Appointment item = mList.get(position);
        Date date = new Date(item.getTime());
        ((ViewHolder) viewHolder).tvTime.setText(FormatUtils.dateToString(date, "yyyy/MM/dd HH:mm"));

        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < item.getServiceItemList().size(); i++) {
            ServiceItem serviceItem = item.getServiceItemList().get(i);
            if (i == item.getServiceItemList().size() - 1) {
                stringBuilder.append(serviceItem.getName()).append(" ").append(serviceItem.getPrice());
            } else {
                stringBuilder.append(serviceItem.getName()).append(" ").append(serviceItem.getPrice()).append("\n");
            }
        }
        if (TextUtils.isEmpty(stringBuilder)) {
            ((ViewHolder) viewHolder).tvServiceItem.setVisibility(View.GONE);
        } else {
            ((ViewHolder) viewHolder).tvServiceItem.setVisibility(View.VISIBLE);
            ((ViewHolder) viewHolder).tvServiceItem.setText(stringBuilder);
        }

        ((ViewHolder) viewHolder).ivMore.setTag(item);
        ((ViewHolder) viewHolder).ivMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mMoreOnClickListener != null) {
                    mMoreOnClickListener.onClick(view);
                }
            }
        });
        ((ViewHolder) viewHolder).itemView.setTag(item);
        ((ViewHolder) viewHolder).itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnClickListener != null) {
                    mOnClickListener.onClick(view);
                }
            }
        });
    }


    @Override
    public int getItemCount() {
        if (mList == null) {
            return 0;
        }
        return mList.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private TextView tvTime;
        private TextView tvServiceItem;
        private ImageView ivMore;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTime = itemView.findViewById(R.id.tvTime);
            tvServiceItem = itemView.findViewById(R.id.tvServiceItem);
            ivMore = itemView.findViewById(R.id.ivMore);
        }

    }
}