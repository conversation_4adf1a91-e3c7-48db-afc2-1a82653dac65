package com.one.appointment.fragment.service.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.one.appointment.R;
import com.one.appointment.entity.ServiceItem;

import java.util.List;

public class ServiceItemAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final Context mContext;
    private View.OnClickListener mOnMoreClickListener;
    private List<ServiceItem> mList;
    private View.OnClickListener mOnItemClickListener;

    public ServiceItemAdapter(Context context, List<ServiceItem> list) {
        mContext = context;
        mList = list;
    }

    public void setItemOnClick(View.OnClickListener onClickListener) {
        mOnItemClickListener = onClickListener;
    }

    public void setMoreOnClick(View.OnClickListener onClickListener) {
        mOnMoreClickListener = onClickListener;
    }

    public void setList(List<ServiceItem> list) {
        mList = list;
        notifyDataSetChanged();
    }

    public void clearList() {
        mList.clear();
        notifyDataSetChanged();
    }

    public void addList(List<ServiceItem> list) {
        if (list == null) {
            return;
        }
        mList.addAll(list);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        return new ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_service, viewGroup, false));
    }


    @SuppressLint("SetTextI18n")
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        final ServiceItem item = mList.get(position);
        if (mOnMoreClickListener != null) {
            ((ViewHolder) viewHolder).ivMore.setVisibility(View.VISIBLE);
        } else {
            ((ViewHolder) viewHolder).ivMore.setVisibility(View.GONE);
        }

        if (item == null) {
            return;
        }
        ((ViewHolder) viewHolder).tvPrice.setText(String.valueOf(item.getPrice()));
        ((ViewHolder) viewHolder).tvName.setText(item.getName());
        ((ViewHolder) viewHolder).tvServiceTime.setText(String.valueOf(item.getServiceTime()));
        ((ViewHolder) viewHolder).tvDescription.setText(item.getDescription());
        ((ViewHolder) viewHolder).ivMore.setTag(item);
        ((ViewHolder) viewHolder).ivMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnMoreClickListener != null) {
                    mOnMoreClickListener.onClick(view);
                }
            }
        });
        ((ViewHolder) viewHolder).itemView.setTag(item);
        ((ViewHolder) viewHolder).itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onClick(view);
                }
            }
        });
    }


    @Override
    public int getItemCount() {
        if (mList == null) {
            return 0;
        }
        return mList.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private TextView tvName;
        private TextView tvPrice;
        private TextView tvServiceTime;
        private TextView tvDescription;
        private ImageView ivMore;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.tvName);
            tvPrice = itemView.findViewById(R.id.tvPrice);
            tvServiceTime = itemView.findViewById(R.id.tvServiceTime);
            tvDescription = itemView.findViewById(R.id.tvDescription);
            ivMore = itemView.findViewById(R.id.ivMore);

        }

    }


}
