package com.one.appointment.fragment.service;


import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.google.android.material.snackbar.Snackbar;
import com.one.appointment.ObjectBox;
import com.one.appointment.R;
import com.one.appointment.constant.KeyDefine;
import com.one.appointment.entity.ServiceItem;
import com.one.appointment.entity.ServiceItem_;
import com.one.appointment.fragment.BaseFragment;
import com.one.appointment.util.FormatUtils;
import com.one.appointment.util.InputTools;

import io.objectbox.Box;

/**
 * A simple {@link Fragment} subclass.
 */
public class AddServiceFragment extends BaseFragment {

    private EditText etName;
    private EditText etPrice;
    private EditText etTime;
    private EditText etDescription;
    private EditText etNumber;
    private Box<ServiceItem> mServiceItemBox;
    private ServiceItem mServiceItem;

    public AddServiceFragment() {
        // Required empty public constructor
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_add_service, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mServiceItemBox = ObjectBox.get().boxFor(ServiceItem.class);
        etName = view.findViewById(R.id.etName);
        etPrice = view.findViewById(R.id.etPrice);
        etTime = view.findViewById(R.id.etTime);
        etDescription = view.findViewById(R.id.etDescription);
        etNumber = view.findViewById(R.id.etNumber);
        Button btAdd = view.findViewById(R.id.btAdd);
        if (getArguments() != null) {
            long id = getArguments().getLong(KeyDefine.ID, -1);

            if (id != -1) {
                mServiceItem = mServiceItemBox.query().equal(ServiceItem_.id, id).build().findFirst();
                if (mServiceItem != null) {
                    etName.setText(mServiceItem.getName());
                    etPrice.setText(String.valueOf(mServiceItem.getPrice()));
                    etTime.setText(String.valueOf(mServiceItem.getServiceTime()));
                    etDescription.setText(mServiceItem.getDescription());
                    btAdd.setText("儲存");
                }
            }
        }
        btAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (TextUtils.isEmpty(etName.getText().toString()) ||
                        TextUtils.isEmpty(etPrice.getText().toString())) {
                    InputTools.HideKeyboard(view);
                    Snackbar.make(etName, getString(R.string.please_fill_in_the_information), Snackbar.LENGTH_LONG).show();
                    return;
                }

                if (mServiceItem == null) {
                    mServiceItem = new ServiceItem();
                }
                mServiceItem.setName(etName.getText().toString());
                if (!TextUtils.isEmpty(etPrice.getText().toString())) {
                    mServiceItem.setPrice(FormatUtils.getInt(etPrice.getText().toString()));
                }
                if (!TextUtils.isEmpty(etTime.getText().toString())) {
                    mServiceItem.setServiceTime(FormatUtils.getInt(etTime.getText().toString()));
                }
                mServiceItem.setDescription(etDescription.getText().toString());
                if (!TextUtils.isEmpty(etNumber.getText().toString())) {
                    mServiceItem.setNumber(FormatUtils.getInt(etNumber.getText().toString()));
                }

                mServiceItemBox.put(mServiceItem);
                getAppCompatActivity().finish();
            }
        });
    }

    @Override
    public void initView(View view) {

    }

}
