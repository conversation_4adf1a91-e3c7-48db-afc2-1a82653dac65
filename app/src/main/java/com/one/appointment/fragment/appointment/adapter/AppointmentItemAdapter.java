package com.one.appointment.fragment.appointment.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.one.appointment.R;
import com.one.appointment.entity.Appointment;
import com.one.appointment.util.FormatUtils;

import java.io.File;
import java.util.Date;
import java.util.List;

public class AppointmentItemAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final Context mContext;
    private View.OnClickListener mOnClickListener;
    private List<Appointment> mList;
    private View.OnClickListener mMoreOnClickListener;

    public AppointmentItemAdapter(Context context, List<Appointment> list) {
        mContext = context;
        mList = list;
    }

    public void setItemOnClick(View.OnClickListener onClickListener) {
        mOnClickListener = onClickListener;
    }

    public void setMoreOnClick(View.OnClickListener onClickListener) {
        mMoreOnClickListener = onClickListener;
    }

    public void setList(List<Appointment> list) {
        mList = list;
        notifyDataSetChanged();
    }

    public void clearList() {
        mList.clear();
        notifyDataSetChanged();
    }

    public void addList(List<Appointment> list) {
        if (list == null) {
            return;
        }
        mList.addAll(list);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        return new ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_appointment, viewGroup, false));
    }


    @SuppressLint("SetTextI18n")
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        final Appointment item = mList.get(position);
        if (item.getCustomer().getTarget() != null) {
            if (!TextUtils.isEmpty(item.getCustomer().getTarget().getAvatarImagePath())) {
                File file = new File(item.getCustomer().getTarget().getAvatarImagePath());
                if (file.exists()) {
                    Glide.with(mContext)
                            .load(file)
                            .into(((ViewHolder) viewHolder).ivAvatar);
                } else {
                    ((ViewHolder) viewHolder).ivAvatar.setImageResource(R.drawable.ic_person_blue_24dp);
                }
            }
            if (TextUtils.isEmpty(item.getCustomer().getTarget().getName())) {
                ((ViewHolder) viewHolder).tvCustomerName.setVisibility(View.GONE);
            } else {
                ((ViewHolder) viewHolder).tvCustomerName.setVisibility(View.VISIBLE);
                ((ViewHolder) viewHolder).tvCustomerName.setText(item.getCustomer().getTarget().getName());
            }
            if (TextUtils.isEmpty(item.getCustomer().getTarget().getNickName())) {
                ((ViewHolder) viewHolder).tvCustomerNickName.setVisibility(View.GONE);
            } else {
                ((ViewHolder) viewHolder).tvCustomerNickName.setVisibility(View.VISIBLE);
                ((ViewHolder) viewHolder).tvCustomerNickName.setText(item.getCustomer().getTarget().getNickName());
            }
        }


        Date date = new Date(item.getTime());
        ((ViewHolder) viewHolder).tvTime.setText(FormatUtils.dateToString(date, "yyyy/MM/dd HH:mm"));


        StringBuilder serviceItem = new StringBuilder();
        for (int i = 0; i < item.getServiceItemList().size(); i++) {
            serviceItem.append(item.getServiceItemList().get(i).getName()).append(" ");
        }
        if (TextUtils.isEmpty(serviceItem)) {
            ((ViewHolder) viewHolder).tvServiceItem.setVisibility(View.GONE);
        } else {
            ((ViewHolder) viewHolder).tvServiceItem.setVisibility(View.VISIBLE);
            ((ViewHolder) viewHolder).tvServiceItem.setText(serviceItem);
        }

        ((ViewHolder) viewHolder).ivDelete.setTag(item);
        ((ViewHolder) viewHolder).ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mMoreOnClickListener != null) {
                    mMoreOnClickListener.onClick(view);
                }
            }
        });
        ((ViewHolder) viewHolder).itemView.setTag(item);
        ((ViewHolder) viewHolder).itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnClickListener != null) {
                    mOnClickListener.onClick(view);
                }
            }
        });
    }


    @Override
    public int getItemCount() {
        if (mList == null) {
            return 0;
        }
        return mList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivAvatar;
        private TextView tvCustomerName;
        private TextView tvCustomerNickName;
        private TextView tvTime;
        private TextView tvServiceItem;
        private ImageView ivDelete;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivAvatar = itemView.findViewById(R.id.ivAvatar);
            tvCustomerName = itemView.findViewById(R.id.tvCustomerName);
            tvCustomerNickName = itemView.findViewById(R.id.tvCustomerNickName);
            tvTime = itemView.findViewById(R.id.tvTime);
            tvServiceItem = itemView.findViewById(R.id.tvServiceItem);
            ivDelete = itemView.findViewById(R.id.ivDelete);
        }

    }


}
