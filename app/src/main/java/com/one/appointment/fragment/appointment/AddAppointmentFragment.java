package com.one.appointment.fragment.appointment;


import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.TimePicker;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.snackbar.Snackbar;
import com.one.appointment.ObjectBox;
import com.one.appointment.R;
import com.one.appointment.constant.KeyDefine;
import com.one.appointment.entity.Appointment;
import com.one.appointment.entity.Appointment_;
import com.one.appointment.entity.Customer;
import com.one.appointment.entity.Customer_;
import com.one.appointment.entity.ServiceItem;
import com.one.appointment.entity.ServiceItem_;
import com.one.appointment.fragment.BaseFragment;
import com.one.appointment.fragment.service.adapter.ServiceItemAdapter;
import com.one.appointment.util.AlarmTimer;
import com.one.appointment.util.FormatUtils;
import com.one.appointment.util.InputTools;
import com.one.appointment.util.LogUtil;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.TimeZone;

import io.objectbox.Box;
import io.objectbox.query.QueryBuilder;

/**
 * 加入預約
 */
public class AddAppointmentFragment extends BaseFragment {
    private EditText etName;
    private EditText etNickName;
    private TextView tvDate;
    private Button btAdd;
    private Box<Appointment> itemBox;
    private Customer mCustomer;
    private Date mDate;
    private String strServiceItem;
    private Box<Customer> mCustomerBox;
    private TextView tvAddServiceItem;
    private RecyclerView mRecyclerView;
    private SwipeRefreshLayout mSwipeRefreshLayout;
    private ServiceItemAdapter mAdapter;
    private Box<Appointment> mAppointmentBox;
    private Appointment mAppointment;
    private TextView tvAddCustomer;
    private TextView tvTime;
    private String imagePath;


    public AddAppointmentFragment() {
        // Required empty public constructor
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_add_appointment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        etName = view.findViewById(R.id.etName);
        etNickName = view.findViewById(R.id.etNickName);
        tvDate = view.findViewById(R.id.tvDate);
        tvTime = view.findViewById(R.id.tvTime);
        btAdd = view.findViewById(R.id.btAdd);
        tvAddServiceItem = view.findViewById(R.id.tvAddServiceItem);
        tvAddCustomer = view.findViewById(R.id.tvAddCustomer);
        tvAddServiceItem.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showServiceItemDialogFragment();
            }
        });

        tvAddCustomer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showCustomerDialogFragment();
            }
        });

        mRecyclerView = view.findViewById(R.id.recyclerView);
        mSwipeRefreshLayout = view.findViewById(R.id.swipeRefreshLayout);
        initRecycleView();

        tvDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                datePicker(tvDate);
            }
        });

        tvTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                timePickerDialog();
            }
        });

        itemBox = ObjectBox.get().boxFor(Appointment.class);
        mAppointment = new Appointment();
        mCustomer = new Customer();

        if (getArguments() != null) {
            String nickName = getArguments().getString(KeyDefine.NICK_NAME, "");
            mDate = (Date) getArguments().getSerializable(KeyDefine.DATE);
            strServiceItem = getArguments().getString(KeyDefine.SERVICE_ITEM, "");
            imagePath = getArguments().getString(KeyDefine.IMAGE_PATH, "");

            // 處理從 Compose Calendar 傳來的選中日期
            long selectedDateTimestamp = getArguments().getLong("selectedDate", -1);
            if (selectedDateTimestamp != -1 && mDate == null) {
                mDate = new Date(selectedDateTimestamp);
                LogUtil.d("✅ 從 Compose Calendar 接收到選中日期: " + FormatUtils.dateToString(mDate, "yyyy/MM/dd"));
            }

            setServiceItem();

            if (mDate != null) {
                tvDate.setText(FormatUtils.dateToString(mDate, "yyyy/MM/dd"));
                tvTime.setText(FormatUtils.dateToString(mDate, "HH:mm"));
            }
            etNickName.setText(nickName);
            Box<Customer> customerBox = ObjectBox.get().boxFor(Customer.class);
            mCustomer = customerBox.query().equal(Customer_.nickName, nickName, QueryBuilder.StringOrder.CASE_SENSITIVE).build().findFirst();

            long customerId = getArguments().getLong(KeyDefine.CUSTOMER_ID, -1);
            if (customerId != -1) {
                mCustomerBox = ObjectBox.get().boxFor(Customer.class);
                mCustomer = mCustomerBox.query().equal(Customer_.id, customerId).build().findFirst();
            }

            if (mCustomer != null) {
                etNickName.setText(mCustomer.getNickName());
                etName.setText(mCustomer.getName());
            }

            long id = getArguments().getLong(KeyDefine.ID, -1);

            if (id != -1) {
                mAppointmentBox = ObjectBox.get().boxFor(Appointment.class);
                mAppointment = mAppointmentBox.query().equal(Appointment_.id, id).build().findFirst();
                if (mAppointment != null) {
                    mCustomer = mAppointment.getCustomer().getTarget();
                    if (mCustomer != null) {
                        etNickName.setText(mCustomer.getNickName());
                        etName.setText(mCustomer.getName());
                    }

                    mDate = new Date(mAppointment.getTime());
                    tvDate.setText(FormatUtils.dateToString(mDate, "yyyy/MM/dd"));
                    tvTime.setText(FormatUtils.dateToString(mDate, "HH:mm"));
                    btAdd.setText("儲存");
                }
            }
        }

        btAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (TextUtils.isEmpty(etNickName.getText().toString()) ||
                        TextUtils.isEmpty(tvDate.getText().toString()) ||
                        TextUtils.isEmpty(tvTime.getText().toString())) {
                    InputTools.HideKeyboard(view);
                    Snackbar.make(btAdd, getString(R.string.please_fill_in_the_information), Snackbar.LENGTH_LONG).show();
                    return;
                }
                mDate = FormatUtils.stringToDate(tvDate.getText().toString() + " " + tvTime.getText().toString(), "yyyy/MM/dd HH:mm");
                if (mCustomer == null) {
                    mCustomer = new Customer();
                }
                mCustomer.setName(etName.getText().toString());
                mCustomer.setNickName(etNickName.getText().toString());
                if (TextUtils.isEmpty(mCustomer.getAvatarImagePath()) && !TextUtils.isEmpty(imagePath)) {
                    mCustomer.setAvatarImagePath(imagePath);
                }
                mAppointment.getCustomer().setTarget(mCustomer);
                mAppointment.setTime(mDate.getTime());
                itemBox.put(mAppointment);

                // 預約時間較目前時間晚的不做定時通知提醒
                Calendar calendar = Calendar.getInstance();
                if ((mDate.getTime() > calendar.getTimeInMillis())) {
                    AlarmTimer.addAlarm(getAppCompatActivity(), mAppointment);
                }

                getAppCompatActivity().finish();
            }
        });

        // 初始化完成後載入數據
        initData();
    }

    private void setServiceItem() {
        Box<ServiceItem> serviceItemBox = ObjectBox.get().boxFor(ServiceItem.class);
        if (strServiceItem.contains("+")) {
            String[] strings = strServiceItem.split("\\+");
            for (String string : strings) {
                ServiceItem serviceItem = serviceItemBox.query().equal(ServiceItem_.name, string, QueryBuilder.StringOrder.CASE_SENSITIVE).build().findFirst();
                if (serviceItem != null) {
                    mAppointment.getServiceItemList().add(serviceItem);
                }
            }
        } else {
            ServiceItem serviceItem = serviceItemBox.query().equal(ServiceItem_.name, strServiceItem, QueryBuilder.StringOrder.CASE_SENSITIVE).build().findFirst();
            if (serviceItem != null) {
                mAppointment.getServiceItemList().add(serviceItem);
            }
        }
    }

    @Override
    public void initView(View view) {

    }

    @Override
    public void onResume() {
        super.onResume();
        // 載入數據並停止刷新動畫
        if (mSwipeRefreshLayout != null && mAdapter != null) {
            mSwipeRefreshLayout.setRefreshing(true);
            initData();
        }
    }

    private void initData() {
        if (mAdapter != null) {
            mAdapter.clearList();
            getData();
        } else {
            // 如果 adapter 還沒初始化，直接停止刷新動畫
            if (mSwipeRefreshLayout != null) {
                mSwipeRefreshLayout.setRefreshing(false);
            }
        }
    }

    private void getData() {
        try {
            List<ServiceItem> list = mAppointment != null ? mAppointment.getServiceItemList() : new ArrayList<>();
            if (mAdapter != null) {
                mAdapter.addList(list);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 確保總是停止刷新動畫
            if (mSwipeRefreshLayout != null) {
                mSwipeRefreshLayout.setRefreshing(false);
            }
        }
    }

    private void initRecycleView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        layoutManager.setOrientation(RecyclerView.VERTICAL);
        mRecyclerView.setLayoutManager(layoutManager);
        mAdapter = new ServiceItemAdapter(getActivity(), new ArrayList<>());
        mAdapter.setMoreOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final ServiceItem serviceItem = (ServiceItem) view.getTag();
                mAppointment.getServiceItemList().remove(serviceItem);
                initData();
            }
        });
        mRecyclerView.setAdapter(mAdapter);

        // 設定下拉刷新
        mSwipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                initData();
                mSwipeRefreshLayout.setRefreshing(false);
            }
        });
    }

    private void datePicker(TextView textView) {
        Calendar calendar = Calendar.getInstance();
        TimeZone timeZone = TimeZone.getTimeZone("GMT+8");
        calendar.setTimeZone(timeZone);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DAY_OF_MONTH);

        DatePickerDialog datePickerDialog = new DatePickerDialog(textView.getContext(), new DatePickerDialog.OnDateSetListener() {
            @Override
            public void onDateSet(DatePicker view, int year, int month, int day) {
                Calendar calendar = new GregorianCalendar(year, month, day);
                TimeZone timeZone = TimeZone.getTimeZone("GMT+8");
                calendar.setTimeZone(timeZone);
                textView.setText(FormatUtils.longToString(calendar.getTimeInMillis(), "yyyy/MM/dd"));

                mDate = new Date(calendar.getTimeInMillis());

                String timeString = FormatUtils.dateToString(mDate, "yyyy/MM/dd");
                tvDate.setText(timeString);

            }
        }, year, month, day);
        datePickerDialog.show();
    }

    private void timePickerDialog() {
        Calendar calendar = Calendar.getInstance();
        TimePickerDialog timePickerDialog = new TimePickerDialog(getAppCompatActivity(),
                new TimePickerDialog.OnTimeSetListener() {
                    @Override
                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                        mDate = FormatUtils.stringToDate(tvDate.getText().toString(), "yyyy/MM/dd");
                        if (mDate != null) {
                            long time = mDate.getTime() + (hourOfDay * 60 * 60 * 1000) + minute * 60 * 1000;
                            String timeString = FormatUtils.longToString(time, "HH:mm");
                            mDate = FormatUtils.stringToDate(timeString, "yyyy/MM/dd HH:mm");
                            tvTime.setText(timeString);
                        }
                    }
                }, calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE), true);
        timePickerDialog.show();
    }

    private void showServiceItemDialogFragment() {
        Box<ServiceItem> serviceItemBox = ObjectBox.get().boxFor(ServiceItem.class);
        List<ServiceItem> list = serviceItemBox.query().orderDesc(ServiceItem_.id).build().find();
        if (list.size() == 0) {
            InputTools.HideKeyboard(btAdd);
            Snackbar.make(btAdd, "請新增服務項目", Snackbar.LENGTH_LONG).show();
            return;
        }
        ServiceItemDialogFragment serviceItemDialogFragment = new ServiceItemDialogFragment(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final ServiceItem serviceItem = (ServiceItem) view.getTag();
                mAppointment.getServiceItemList().add(serviceItem);
                initData();
            }
        });
        serviceItemDialogFragment.show(getAppCompatActivity().getSupportFragmentManager(), "showServiceItemDialogFragment");
    }

    private void showCustomerDialogFragment() {
        Box<Customer> serviceItemBox = ObjectBox.get().boxFor(Customer.class);
        List<Customer> list = serviceItemBox.query().orderDesc(Customer_.id).build().find();
        if (list.size() == 0) {
            InputTools.HideKeyboard(btAdd);
            Snackbar.make(btAdd, "請新增顧客", Snackbar.LENGTH_LONG).show();
            return;
        }
        CustomerDialogFragment customerDialogFragment = new CustomerDialogFragment(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mCustomer = (Customer) view.getTag();
                mAppointment.getCustomer().setTarget(mCustomer);
                etName.setText(mCustomer.getName());
                etNickName.setText(mCustomer.getNickName());
                initData();
            }
        });
        customerDialogFragment.show(getAppCompatActivity().getSupportFragmentManager(), "CustomerDialogFragment");
    }
}
