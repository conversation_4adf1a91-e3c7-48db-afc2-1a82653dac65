package com.one.appointment.fragment.customer;


import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bumptech.glide.Glide;
import com.one.appointment.ObjectBox;
import com.one.appointment.R;
import com.one.appointment.activity.ToolbarActivity;
import com.one.appointment.constant.EntryPoint;
import com.one.appointment.constant.KeyDefine;
import com.one.appointment.entity.Appointment;
import com.one.appointment.entity.Appointment_;
import com.one.appointment.entity.Customer;
import com.one.appointment.entity.Customer_;
import com.one.appointment.fragment.BaseFragment;
import com.one.appointment.fragment.customer.adapter.AppointmentRecordItemAdapter;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import io.objectbox.Box;

/**
 * A simple {@link Fragment} subclass.
 */
public class AppointmentRecordFragment extends BaseFragment {

    private RecyclerView mRecyclerView;
    private SwipeRefreshLayout mSwipeRefreshLayout;
    private Box<Appointment> mAppointmentBox;
    private AppointmentRecordItemAdapter mAdapter;
    private long mCustomerId;
    private TextView tvTotalPrice;
    private ImageView ivAvatar;
    private TextView tvNickName;
    private TextView tvName;
    private View layoutEmpty;

    public AppointmentRecordFragment() {
        // Required empty public constructor
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_appointment_record, container, false);
    }

    @Override
    public void initView(View view) {
        layoutEmpty = view.findViewById(R.id.layoutEmpty);
        TextView tvEmptyDesc = layoutEmpty.findViewById(R.id.tvEmptyDesc);
        tvEmptyDesc.setText("尚無預約");

        tvTotalPrice = view.findViewById(R.id.tvTotalPrice);
        mRecyclerView = view.findViewById(R.id.recyclerView);
        mSwipeRefreshLayout = view.findViewById(R.id.swipeRefreshLayout);
        ivAvatar = view.findViewById(R.id.ivAvatar);
        tvNickName = view.findViewById(R.id.tvNickName);
        tvName = view.findViewById(R.id.tvName);

        initRecycleView();

        if (getArguments() != null) {
            mCustomerId = getArguments().getLong(KeyDefine.CUSTOMER_ID, -1);
            Box<Customer> customerBox = ObjectBox.get().boxFor(Customer.class);
            Customer customer = customerBox.query().equal(Customer_.id, mCustomerId).build().findFirst();

            if (customer != null) {
                if (!TextUtils.isEmpty(customer.getAvatarImagePath())) {
                    File file = new File(customer.getAvatarImagePath());
                    if (file.exists()) {
                        Glide.with(getAppCompatActivity())
                                .load(file) // Uri of the picture
                                .into(ivAvatar);
                    } else {
                        ivAvatar.setImageResource(R.drawable.ic_person_blue_24dp);
                    }
                }
                tvNickName.setText(customer.getNickName());
                tvName.setText(customer.getName());
            }

        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mSwipeRefreshLayout != null) {
            mSwipeRefreshLayout.setRefreshing(true);
        }
    }

    private void initData() {
        mAdapter.clearList();
        getData();
    }

    private void getData() {
        mAppointmentBox = ObjectBox.get().boxFor(Appointment.class);
        List<Appointment> list = mAppointmentBox.query().equal(Appointment_.customerId, mCustomerId).build().find();
        mAdapter.addList(list);
        int totalPrice = 0;
        for (int i = 0; i < list.size(); i++) {
            for (int j = 0; j < list.get(i).getServiceItemList().size(); j++) {
                totalPrice += list.get(i).getServiceItemList().get(j).getPrice();
            }
        }
        if (list.size() == 0) {
            layoutEmpty.setVisibility(View.VISIBLE);
        } else {
            layoutEmpty.setVisibility(View.GONE);
        }
        tvTotalPrice.setText(String.valueOf(totalPrice));

        // 停止刷新動畫
        mSwipeRefreshLayout.setRefreshing(false);
    }

    private void initRecycleView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        layoutManager.setOrientation(RecyclerView.VERTICAL);
        mRecyclerView.setLayoutManager(layoutManager);
        mAdapter = new AppointmentRecordItemAdapter(getActivity(), new ArrayList<>());
        mAdapter.setMoreOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final Appointment appointment = (Appointment) view.getTag();
                mAppointmentBox.remove(appointment);
                initData();
            }
        });
        mAdapter.setItemOnClick(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final Appointment appointment = (Appointment) view.getTag();
                Bundle bundle = new Bundle();
                bundle.putLong(KeyDefine.ID, appointment.id);
                ToolbarActivity.toActivity(getAppCompatActivity(), EntryPoint.AddAppointmentFragment, bundle);
            }
        });
        mRecyclerView.setAdapter(mAdapter);

        // 設定下拉刷新
        mSwipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                //refresh data here
                initData();
                mSwipeRefreshLayout.setRefreshing(false);
            }
        });
    }


}
