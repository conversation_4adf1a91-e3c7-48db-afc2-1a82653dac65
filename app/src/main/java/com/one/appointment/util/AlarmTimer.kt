package com.one.appointment.util

import android.content.Intent
import android.app.PendingIntent
import android.app.AlarmManager
import android.content.Context
import android.os.Build
import com.one.appointment.constant.PrefKey
import com.one.appointment.entity.Appointment
import com.one.appointment.receiver.AlarmReceiver
import android.os.Bundle
import java.util.*

/**
 * 鬧鐘定時工具類
 *
 * <AUTHOR>
 * @time 2016/12/13 10:03
 */
object AlarmTimer {
    /**
     * 設定週期性鬧鐘
     *
     * @param context
     * @param firstTime
     * @param cycTime
     * @param action
     * @param AlarmManagerType 鬧鐘的型別，常用的有5個值：AlarmManager.ELAPSED_REALTIME、
     * AlarmManager.ELAPSED_REALTIME_WAKEUP、AlarmManager.RTC、
     * AlarmManager.RTC_WAKEUP、AlarmManager.POWER_OFF_WAKEUP
     */
    fun setRepeatingAlarmTimer(
        context: Context, firstTime: Long,
        cycTime: Long, action: String?, AlarmManagerType: Int
    ) {
        val myIntent = Intent()
        myIntent.action = action
        val sender = PendingIntent.getBroadcast(context, 0, myIntent, 0)
        val alarm = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        alarm.setRepeating(AlarmManagerType, firstTime, cycTime, sender)
        //param1：鬧鐘型別，param1：鬧鐘首次執行時間，param1：鬧鐘兩次執行的間隔時間，param1：鬧鐘響應動作。
    }

    /**
     * 設定定時鬧鐘
     *
     * @param context
     * @param cycTime
     * @param action
     * @param AlarmManagerType 鬧鐘的型別，常用的有5個值：AlarmManager.ELAPSED_REALTIME、
     * AlarmManager.ELAPSED_REALTIME_WAKEUP、AlarmManager.RTC、
     * AlarmManager.RTC_WAKEUP、AlarmManager.POWER_OFF_WAKEUP
     */
    fun setAlarmTimer(
        context: Context, cycTime: Long,
        action: String?, AlarmManagerType: Int
    ) {
        val myIntent = Intent()
        //傳遞定時日期
//        myIntent.putExtra("date", date);
        myIntent.action = action
        //給每個鬧鐘設定不同ID防止覆蓋
        val alarmId = PrefUtils.getFromPrefs(context, PrefKey.ALARM_ID, 0)
        PrefUtils.saveToPrefs(context, PrefKey.ALARM_ID, alarmId)
        val sender = PendingIntent.getBroadcast(context, alarmId, myIntent, 0)
        val alarm = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        alarm[AlarmManagerType, cycTime] = sender
    }

    /**
     * 取消鬧鐘
     *
     * @param context
     * @param action
     */
    fun cancelAlarmTimer(context: Context, action: String?) {
        val myIntent = Intent()
        myIntent.action = action
        val sender = PendingIntent.getBroadcast(context, 0, myIntent, 0)
        val alarm = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        alarm.cancel(sender)
    }

    /***    加入(與系統註冊)鬧鐘     */
    @JvmStatic
    fun addAlarm(context: Context, appointment: Appointment) {
        val cal = Calendar.getInstance()
        cal.timeInMillis = appointment.time
        LogUtil.d("alarm add time: " + cal[Calendar.MONTH] + "." + cal[Calendar.DATE] + " " + cal[Calendar.HOUR_OF_DAY] + ":" + cal[Calendar.MINUTE] + ":" + cal[Calendar.SECOND])
        val intent = Intent(context, AlarmReceiver::class.java)
        // 以日期字串組出不同的 category 以添加多個鬧鐘
        intent.addCategory("ID." + cal[Calendar.MONTH] + "." + cal[Calendar.DATE] + "-" + cal[Calendar.HOUR_OF_DAY] + "." + cal[Calendar.MINUTE] + "." + cal[Calendar.SECOND])
        val AlarmTimeTag =
            "Alarmtime " + cal[Calendar.HOUR_OF_DAY] + ":" + cal[Calendar.MINUTE] + ":" + cal[Calendar.SECOND]
        val bundle = Bundle()
        //        bundle.putSerializable(KeyDefine.APPOINTMENT, appointment);
        bundle.putString("title", appointment.customer.target.nickName)
        var item = ""
        for (i in appointment.serviceItemList.indices) {
            item += appointment.serviceItemList[i].name + " "
        }
        bundle.putString("item", item)
        intent.putExtra("data", bundle)
        val pendingIntent: PendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getBroadcast(context, 1, intent, PendingIntent.FLAG_MUTABLE)
        } else {
            PendingIntent.getBroadcast(context, 1, intent, PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE)
        }
        val am = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        am[AlarmManager.RTC_WAKEUP, cal.timeInMillis] = pendingIntent //註冊鬧鐘
    }

    /***    取消(與系統註冊的)鬧鐘     */
    private fun cancelAlarm(context: Context, cal: Calendar) {
        LogUtil.d("alarm cancel time: " + cal[Calendar.MONTH] + "." + cal[Calendar.DATE] + " " + cal[Calendar.HOUR_OF_DAY] + ":" + cal[Calendar.MINUTE] + ":" + cal[Calendar.SECOND])
        val intent = Intent(context, AlarmReceiver::class.java)
        // 以日期字串組出不同的 category 以添加多個鬧鐘
        intent.addCategory("ID." + cal[Calendar.MONTH] + "." + cal[Calendar.DATE] + "-" + cal[Calendar.HOUR_OF_DAY] + "." + cal[Calendar.MINUTE] + "." + cal[Calendar.SECOND])
        val AlarmTimeTag =
            "Alarmtime " + cal[Calendar.HOUR_OF_DAY] + ":" + cal[Calendar.MINUTE] + ":" + cal[Calendar.SECOND]
        intent.putExtra("title", "activity_app")
        intent.putExtra("time", AlarmTimeTag)
        val pi = PendingIntent.getBroadcast(context, 1, intent, PendingIntent.FLAG_UPDATE_CURRENT)
        val am = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        am.cancel(pi) //取消鬧鐘，只差在這裡
    }
}