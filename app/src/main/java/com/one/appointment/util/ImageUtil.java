package com.one.appointment.util;

import android.content.Context;
import android.os.Environment;

import java.io.File;

public class ImageUtil {

    public static String getDirPath(Context context, String dirName) {
        String root = Environment.getExternalStorageDirectory().toString();
        return root + "/" + context.getPackageName() + "/" + dirName;
    }

    public static String getImagePath(Context context, String dirName, String nickName) {
        String root = Environment.getExternalStorageDirectory().toString();
        return root + "/" + context.getPackageName() + "/" + dirName + "/" + nickName + ".temp";
    }

    public static File getImageFile(Context context, String packageName, String nickName) {
        return new File(getImagePath(context, packageName, nickName));
    }
}
