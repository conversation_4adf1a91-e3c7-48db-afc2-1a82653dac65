package com.one.appointment.util

import android.content.Context
import android.util.Log
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability

/**
 * Google Services 工具類
 * 用於處理 Google Play Services 相關的錯誤和檢查
 */
object GoogleServicesUtil {
    
    private const val TAG = "GoogleServicesUtil"
    
    /**
     * 檢查 Google Play Services 是否可用
     */
    fun isGooglePlayServicesAvailable(context: Context): <PERSON><PERSON>an {
        return try {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)
            
            when (resultCode) {
                ConnectionResult.SUCCESS -> {
                    Log.d(TAG, "Google Play Services 可用")
                    true
                }
                ConnectionResult.SERVICE_MISSING -> {
                    Log.w(TAG, "Google Play Services 未安裝")
                    false
                }
                ConnectionResult.SERVICE_VERSION_UPDATE_REQUIRED -> {
                    Log.w(TAG, "Google Play Services 需要更新")
                    false
                }
                ConnectionResult.SERVICE_DISABLED -> {
                    Log.w(TAG, "Google Play Services 已停用")
                    false
                }
                else -> {
                    Log.w(TAG, "Google Play Services 不可用，錯誤代碼: $resultCode")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "檢查 Google Play Services 時發生錯誤: ${e.message}")
            false
        }
    }
    
    /**
     * 安全地初始化 Google Services
     * 如果 Google Play Services 不可用，則跳過初始化
     */
    fun safeInitializeGoogleServices(context: Context, onSuccess: () -> Unit, onFailure: () -> Unit) {
        try {
            if (isGooglePlayServicesAvailable(context)) {
                onSuccess()
                Log.d(TAG, "Google Services 初始化成功")
            } else {
                onFailure()
                Log.w(TAG, "Google Services 不可用，跳過初始化")
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "Google Services 安全異常: ${e.message}")
            onFailure()
        } catch (e: Exception) {
            Log.e(TAG, "Google Services 初始化失敗: ${e.message}")
            onFailure()
        }
    }
    
    /**
     * 處理 Google Services 相關的 SecurityException
     */
    fun handleGoogleServicesSecurityException(e: SecurityException) {
        Log.w(TAG, "Google Services 安全異常（通常在模擬器中出現）: ${e.message}")
        Log.w(TAG, "這個錯誤不會影響應用程式的核心功能")
    }
    
    /**
     * 檢查是否在模擬器中運行
     */
    fun isRunningOnEmulator(): Boolean {
        return (android.os.Build.FINGERPRINT.startsWith("generic")
                || android.os.Build.FINGERPRINT.startsWith("unknown")
                || android.os.Build.MODEL.contains("google_sdk")
                || android.os.Build.MODEL.contains("Emulator")
                || android.os.Build.MODEL.contains("Android SDK built for x86")
                || android.os.Build.MANUFACTURER.contains("Genymotion")
                || (android.os.Build.BRAND.startsWith("generic") && android.os.Build.DEVICE.startsWith("generic"))
                || "google_sdk" == android.os.Build.PRODUCT)
    }
    
    /**
     * 獲取 Google Services 狀態描述
     */
    fun getGoogleServicesStatusDescription(context: Context): String {
        return try {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)
            
            when (resultCode) {
                ConnectionResult.SUCCESS -> "Google Play Services 正常運行"
                ConnectionResult.SERVICE_MISSING -> "Google Play Services 未安裝"
                ConnectionResult.SERVICE_VERSION_UPDATE_REQUIRED -> "Google Play Services 需要更新"
                ConnectionResult.SERVICE_DISABLED -> "Google Play Services 已停用"
                ConnectionResult.SERVICE_INVALID -> "Google Play Services 版本無效"
                else -> "Google Play Services 狀態未知 (代碼: $resultCode)"
            }
        } catch (e: Exception) {
            "無法檢查 Google Play Services 狀態: ${e.message}"
        }
    }
}
