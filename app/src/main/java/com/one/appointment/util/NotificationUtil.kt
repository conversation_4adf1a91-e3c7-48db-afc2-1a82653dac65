package com.one.appointment.util

import android.Manifest
import android.app.PendingIntent
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import android.text.TextUtils
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.one.appointment.R
import com.one.appointment.activity.SplashActivity
import com.one.appointment.constant.KeyDefine
import com.one.appointment.service.NotificationCollectorService

object NotificationUtil {

    @JvmStatic
    fun createMessageNotification(context: Context, title: String, text: String) {
        val contentIntent = getContentIntent(context, title, 1)
        val channelId =  context.getString(R.string.channel_message_id)
        createNotification(context, contentIntent, title, text, channelId)
    }

    @JvmStatic
    fun createAlarmNotification(context: Context, title: String, text: String) {
        val contentIntent = getContentIntent(context, title, 0)
        val channelId =  context.getString(R.string.channel_alarm_id)
        createNotification(context, contentIntent, title, text, channelId)
    }

    private fun getContentIntent(
        context: Context,
        pushKey: String,
        index: Int
    ): PendingIntent {
        val notificationIntent = Intent(context, SplashActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        notificationIntent.putExtra(KeyDefine.INDEX, index)
        notificationIntent.action = pushKey

        val contentIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getActivity(
                context,
                0,
                notificationIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
            )
        } else {
            PendingIntent.getActivity(
                context,
                0,
                notificationIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
        }
        return contentIntent
    }

    private fun createNotification(
        context: Context,
        contentIntent: PendingIntent,
        title: String,
        text: String,
        channelId: String
    ) {
        LogUtil.d("showNotification: ")
        val builder = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(text)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(contentIntent)
            .setAutoCancel(true)

//       val areNotificationsEnabled =  notificationManager?.areNotificationsEnabled()
//        LogUtil.d("areNotificationsEnabled $areNotificationsEnabled")
        with(NotificationManagerCompat.from(context)) {
//            if (ActivityCompat.checkSelfPermission(
//                    context,
//                    Manifest.permission.POST_NOTIFICATIONS
//                ) != PackageManager.PERMISSION_GRANTED
//            ) {
//                return
//            }
            notify(
                title,
                channelId.toInt(),
                builder.build()
            )
        }
    }

    // 判断是否拥有通知权限
    fun isNotificationListenersEnabled(context: Context): Boolean {
        val pkgName = context.packageName
        val flat =
            Settings.Secure.getString(context.contentResolver, "enabled_notification_listeners")
        if (!TextUtils.isEmpty(flat)) {
            val names = flat.split(":").toTypedArray()
            for (name in names) {
                val cn = ComponentName.unflattenFromString(name)
                if (cn != null) {
                    if (TextUtils.equals(pkgName, cn.packageName)) {
                        return true
                    }
                }
            }
        }
        return false
    }

    private fun isNotificationListenerServiceEnabled(context: Context): Boolean {
        val packageNames = NotificationManagerCompat.getEnabledListenerPackages(context)
        return packageNames.contains(context.packageName)
    }

    fun enabledNotificationListeners(context: Context) {
        Settings.Secure.getString(
            context.contentResolver,
            "enabled_notification_listeners"
        )
        context.startActivity(
            Intent(
                "android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS"
            )
        )
    }

    //重新开启NotificationMonitor
    @JvmStatic
    fun toggleNotificationListenerService(context: Context) {
        val thisComponent = ComponentName(context, NotificationCollectorService::class.java)
        val pm = context.packageManager
        pm.setComponentEnabledSetting(
            thisComponent,
            PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
            PackageManager.DONT_KILL_APP
        )
        pm.setComponentEnabledSetting(
            thisComponent,
            PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
            PackageManager.DONT_KILL_APP
        )
    }
    //    public static void enabledNotificationListeners(Context context) {
    //        Intent intent = new Intent();
    //        if (Build.VERSION.SDK_INT >= 26) {// android 8.0引导
    //            intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
    //            intent.putExtra("android.provider.extra.APP_PACKAGE", context.getPackageName());
    //        } else if (Build.VERSION.SDK_INT >= 21) { // android 5.0-7.0
    //            intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
    //            intent.putExtra("app_package", context.getPackageName());
    //            intent.putExtra("app_uid", context.getApplicationInfo().uid);
    //        } else {//其它
    //            intent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
    //            intent.setData(Uri.fromParts("package", context.getPackageName(), null));
    //        }
    //        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
    //        context.startActivity(intent);
    //    }
}