package com.one.appointment.util;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


public class LogUtil {

    private final static boolean DEBUG = true;//BuildConfig.IS_DEBUG;


    public static String getClassName() {
        StackTraceElement stackTraceElement = Thread.currentThread().getStackTrace()[4];
        String callerClazzName = stackTraceElement.getClassName();
        String className = callerClazzName.substring(callerClazzName.lastIndexOf(".") + 1);
        return "[" + className + "]";
    }

    private static String getMessage() {
        StackTraceElement stackTraceElement = Thread.currentThread().getStackTrace()[4];
        String methodName = stackTraceElement.getMethodName();
        int lineNumber = stackTraceElement.getLineNumber();
        return "[" + methodName + "][" + lineNumber + "]";
    }

    public static void wtf(String msg) {
        if (DEBUG) {
            Log.wtf(getClassName(), getMessage() + msg);
        }
    }

    public static void wtf(String msg, Throwable tr) {
        if (DEBUG) {
            Log.wtf(getClassName(), getMessage() +
                    msg, tr);
        }
    }


    public static void e() {
        if (DEBUG) {
            Log.e(getClassName(), getMessage());
        }
    }

    public static void e(String msg, Throwable tr) {
        if (DEBUG) {
            Log.e(getClassName(), getMessage() + msg, tr);
        }
    }

    public static void i() {
        if (DEBUG) {
            Log.i(getClassName(), getMessage());
        }
    }

    public static void d() {
        if (DEBUG) {
            Log.d(getClassName(), getMessage());
        }
    }

    public static void e(String message) {
        if (DEBUG) {
            if (message.length() < 4000) {
                Log.e(getClassName(), getMessage() + message);
                return;
            }
            log(getClassName(), getMessage() + message, new LogCallBack() {
                @Override
                public void log(String className, @NonNull String message) {
                    Log.e(className, message);
                }
            });
        }
    }

    public static void d(String message) {
        if (DEBUG) {
            if (message.length() < 4000) {
                Log.d(getClassName(), getMessage() + message);
                return;
            }
            log(getClassName(), getMessage() + message, new LogCallBack() {
                @Override
                public void log(String className, @NonNull String message) {
                    Log.d(className, message);
                }
            });
        }
    }

    public static void i(String message) {
        if (DEBUG) {
            if (message.length() < 4000) {
                Log.i(getClassName(), getMessage() + message);
                return;
            }
            log(getClassName(), getMessage() + message, new LogCallBack() {
                @Override
                public void log(String className, @NonNull String message) {
                    Log.i(className, message);
                }
            });
        }
    }

//    public static String logJson(String readString) {
//        try {
//            JsonParser jsonParser = new JsonParser();
//            JsonObject jsonObject = jsonParser.parse(readString).getAsJsonObject();
//            Gson gson = new GsonBuilder().setPrettyPrinting().create();
//            return "\n" + gson.toJson(jsonObject);
//        } catch (Exception e) {
//            e.printStackTrace();
//            if (readString.length() > 200) {
//                e("ZInterceptor : " + readString.substring(0, 200));
//            } else {
//                e("ZInterceptor : " + readString);
//            }
//        }
//        return "";
//    }

    private static final int MAX_LOG_LENGTH = 3000;

    private static void log(String className, @Nullable String message, @NonNull LogCallBack callback) {
        if (message == null) {
            callback.log(className, "null");
            return;
        }
        for (int i = 0; i <= message.length() / MAX_LOG_LENGTH; i++) {
            int start = i * MAX_LOG_LENGTH;
            int end = (i + 1) * MAX_LOG_LENGTH;
            end = end > message.length() ? message.length() : end;
            callback.log(className, message.substring(start, end));
        }
    }

    private interface LogCallBack {
        void log(String className, @NonNull String message);
    }
}
