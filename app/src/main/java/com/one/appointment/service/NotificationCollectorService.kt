package com.one.appointment.service

import android.app.Notification
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.os.Environment
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.text.TextUtils
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.one.appointment.ObjectBox
import com.one.appointment.R
import com.one.appointment.constant.KeyDefine
import com.one.appointment.constant.PrefKey
import com.one.appointment.entity.Message
import com.one.appointment.entity.Message_
import com.one.appointment.util.ImageUtil
import com.one.appointment.util.LogUtil
import com.one.appointment.util.NotificationUtil.createMessageNotification
import com.one.appointment.util.PrefUtils
import io.objectbox.query.QueryBuilder
import java.io.File
import java.io.FileOutputStream


class NotificationCollectorService : NotificationListenerService() {
    // line, line official account
    var packageNames = arrayOf(
        "jp.naver.line.android",
        "com.linecorp.lineoa",
        "com.facebook.katana",
        "com.facebook.orca",
        "com.instagram.android",
        "org.telegram.messenger",
        "com.google.android.apps.messaging",
        "com.jkos.jello",
        "com.skype.raider",
        "tw.hairbook.photo" // Style Map
    )

    override fun onNotificationPosted(sbn: StatusBarNotification) {
        LogUtil.i("getPackageName" + ":" + sbn.packageName)
        // 過濾通知的包名
        for (packageName in packageNames) {
            if (packageName == sbn.packageName) {
                addMessage(sbn)
                break
            }
        }
    }

    private fun addMessage(sbn: StatusBarNotification) {
        LogUtil.i("tickerText" + ":" + sbn.notification.tickerText)
        LogUtil.i("title" + ":" + sbn.notification.extras["android.title"])
        LogUtil.i("text" + ":" + sbn.notification.extras["android.text"])
        val notification = sbn.notification ?: return
        val extras = notification.extras
        if (extras != null) {
//            PendingIntent pendingIntent = sbn.getNotification().contentIntent; //获取通知的PendingIntent
            val packageName = sbn.packageName
            var notificationTitle = extras.getString(Notification.EXTRA_TITLE, "")
            var notificationText = extras.getString(Notification.EXTRA_TEXT, "")
            //            CharSequence notificationSubText = extras.getCharSequence(Notification.EXTRA_SUB_TEXT, "");
//            int notificationIcon = extras.getInt(Notification.EXTRA_SMALL_ICON);
//            Bitmap notificationLargeIcon = extras.getParcelable(Notification.EXTRA_LARGE_ICON);
            val keyWord =
                PrefUtils.getFromPrefs(this, PrefKey.KEY_WORD, getString(R.string.appointment))
            if (packageName == "tw.hairbook.photo") {
                if (notificationText.contains("想與您預約")) {
                    // StyleMap小天使: Yi Ping Lu 想與您預約 04/03 (週五) 20:30 的洗髮 服務
                    notificationTitle = notificationText.replace("StyleMap小天使: ", "")
                    notificationTitle = notificationTitle.split("想與您預約".toRegex())
                        .dropLastWhile { it.isEmpty() }
                        .toTypedArray()[0]
                    notificationText =
                        notificationText.replace("StyleMap小天使: $notificationTitle", "")
                } else if (notificationText.contains("我已取消")) {
                    // Yi Ping Lu: 抱歉，我已取消 2020/04/03 20:30 的預約
                    notificationTitle =
                        notificationText.split(":".toRegex()).dropLastWhile { it.isEmpty() }
                            .toTypedArray()[0]
                    notificationText = notificationText.replace("$notificationTitle: ", "")
                } else {
                    notificationTitle =
                        notificationText.split(":".toRegex()).dropLastWhile { it.isEmpty() }
                            .toTypedArray()[0]
                    notificationText = notificationText.replace("$notificationTitle: ", "")
                }
            }
            if (!TextUtils.isEmpty(notificationText) && notificationText.contains(keyWord) || packageName == "tw.hairbook.photo") {
                val messageBox = ObjectBox.get().boxFor(
                    Message::class.java
                )
                val builder = messageBox.query()
                //                List<Message> list = builder.build().find();
                val messageList = builder.equal(
                    Message_.title,
                    notificationTitle,
                    QueryBuilder.StringOrder.CASE_SENSITIVE
                )
                    .and().equal(
                        Message_.content,
                        notificationText,
                        QueryBuilder.StringOrder.CASE_SENSITIVE
                    )
                    .and().between(Message_.postTime, sbn.postTime - 200, sbn.postTime + 200)
                    .build().find()
                if (messageList.size > 0) {
                    // 過濾掉重複的訊息
                    return
                }
                val message = Message()
                message.packageName = sbn.packageName
                message.title = notificationTitle
                message.content = notificationText
                message.postTime = sbn.postTime
                if (extras.containsKey(Notification.EXTRA_LARGE_ICON)) {
                    try {
                        val notificationLarge = notification.getLargeIcon().loadDrawable(this)
//                            extras.getParcelable<Bitmap>(Notification.EXTRA_LARGE_ICON)
                        val bitmap = (notificationLarge as BitmapDrawable).bitmap
                        saveTempBitmap(
                            this,
                            bitmap,
                            packageName,
                            notificationTitle
                        )
                        LogUtil.d("notificationLargeIcon : $notificationLarge")
                        message.imagePath = ImageUtil.getImagePath(
                            this, packageName, notificationTitle
                        )
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                messageBox.put(message)
                createMessageNotification(
                    this,
                    "收到" + notificationTitle + "的預約",
                    notificationText
                )
                requestCode++
                // 使用 LocalBroadcastManager 替代 LiveEventBus
                val intent = Intent(KeyDefine.POST_MESSAGE)
                intent.putExtras(extras)
                LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
            }
        }
    }

    companion object {
        var requestCode = 1
    }

    private fun saveTempBitmap(
        context: Context,
        bitmap: Bitmap?,
        packageName: String,
        title: String
    ) {
        if (isExternalStorageWritable) {
            saveImage(context, bitmap, packageName, title)
        } else {
            LogUtil.e("isExternalStorageWritable == false")
            //prompt the user or do something
        }
    }

    private fun saveImage(
        context: Context,
        finalBitmap: Bitmap?,
        packageName: String,
        title: String
    ) {
        val myDir = File(ImageUtil.getDirPath(context, packageName))
        val isMkdirs = myDir.mkdirs()
        LogUtil.d("isDelete : $isMkdirs")
        val file = File(ImageUtil.getImagePath(context, packageName, title))
        if (file.exists()) {
            val isDelete = file.delete()
            LogUtil.d("isDelete : $isDelete")
        }
        try {
            val out = FileOutputStream(file)
            finalBitmap!!.compress(Bitmap.CompressFormat.PNG, 100, out)
            out.flush()
            out.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private val isExternalStorageWritable: Boolean
        /* Checks if external storage is available for read and write */
        get() {
            val state = Environment.getExternalStorageState()
            return Environment.MEDIA_MOUNTED == state
        }

    override fun onNotificationRemoved(sbn: StatusBarNotification) {
        LogUtil.i("remove" + "-----" + sbn.packageName)
    }
}