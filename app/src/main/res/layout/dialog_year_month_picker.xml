<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 標題 -->
    <TextView
        android:id="@+id/titleText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="選擇年月"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:gravity="center"
        android:padding="16dp" />

    <!-- 當前選擇顯示 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardBackgroundColor="@color/colorPrimary"
        app:cardElevation="4dp"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <TextView
            android:id="@+id/selectedYearMonthText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2025年 7月"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:gravity="center"
            android:padding="16dp" />

    </com.google.android.material.card.MaterialCardView>

    <!-- 年月選擇區域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:orientation="horizontal">

        <!-- 年份選擇 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="年份"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:gravity="center"
                android:padding="8dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/yearRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:scrollbars="vertical" />

        </LinearLayout>

        <!-- 月份選擇 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1.5"
            android:orientation="vertical"
            android:layout_marginStart="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="月份"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:gravity="center"
                android:padding="8dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/monthRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
