<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/btAdd"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingTop="10dp"
        android:paddingEnd="20dp"
        android:paddingBottom="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/name"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvAddCustomer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:drawableStart="@drawable/ic_add_blue_24dp"
                android:gravity="center"
                android:text="@string/app_fragment_add_appointment_select_customers"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />
        </RelativeLayout>


        <EditText
            android:id="@+id/etName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/bg_edit_text"
            android:hint="請輸入名稱"
            android:importantForAutofill="no"
            android:inputType="text"
            android:padding="10dp"
            android:textSize="16sp"
            tools:text="" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/nick_name"
            android:textColor="@color/primary_text"
            android:textSize="16sp" />

        <EditText
            android:id="@+id/etNickName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/bg_edit_text"
            android:hint="請輸入名稱"
            android:importantForAutofill="no"
            android:inputType="text"
            android:padding="10dp"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:text="@string/app_fragment_add_appointment_appointment_date"
                    android:textColor="@color/primary_text"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvDate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/bg_edit_text"
                    android:gravity="center"
                    android:hint="請選擇預約日期"
                    android:padding="10dp"
                    android:textSize="16sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:text="@string/app_fragment_add_appointment_appointment_time"
                    android:textColor="@color/primary_text"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvTime"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/bg_edit_text"
                    android:gravity="center"
                    android:hint="請選擇預約時間"
                    android:padding="10dp"
                    android:textSize="16sp" />
            </LinearLayout>
        </LinearLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_fragment_add_appointment_service_item"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvAddServiceItem"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:drawableStart="@drawable/ic_add_blue_24dp"
                android:gravity="center"
                android:text="@string/app_fragment_add_appointment_select_service_items"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:background="@color/colorPrimaryDark" />

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

    <Button
        android:id="@+id/btAdd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/btn_primary_dark"
        android:text="@string/add"
        android:textColor="@color/white"
        android:textSize="16sp" />
</RelativeLayout>
