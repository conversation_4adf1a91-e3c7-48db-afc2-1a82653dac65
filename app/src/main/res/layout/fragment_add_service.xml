<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="20dp"
    tools:context=".fragment.customer.AddCustomerFragment">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/service_item"
        android:textColor="@color/primary_text"
        android:textSize="16sp" />

    <EditText
        android:id="@+id/etName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_edit_text"
        android:hint="請輸入服務項目"
        android:importantForAutofill="no"
        android:inputType="text"
        android:padding="10dp"
        android:textSize="16sp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/app_fragment_add_service_service_amount"
        android:textColor="@color/primary_text"
        android:textSize="16sp" />

    <EditText
        android:id="@+id/etPrice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_edit_text"
        android:hint="請輸入服務金額"
        android:importantForAutofill="no"
        android:inputType="numberSigned"
        android:padding="10dp"
        android:textSize="16sp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/app_fragment_add_service_service_time"
        android:textColor="@color/primary_text"
        android:textSize="16sp" />

    <EditText
        android:id="@+id/etTime"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_edit_text"
        android:hint="請輸入服務時間"
        android:importantForAutofill="no"
        android:inputType="numberSigned"
        android:padding="10dp"
        android:textSize="16sp"
        tools:text="" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/app_fragment_add_service_describe"
        android:textColor="@color/primary_text"
        android:textSize="16sp"
        android:visibility="gone" />

    <EditText
        android:id="@+id/etDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_edit_text"
        android:hint="描述"
        android:importantForAutofill="no"
        android:inputType="text"
        android:padding="10dp"
        android:textSize="16sp"
        android:visibility="gone" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/app_fragment_add_service_numbering"
        android:textColor="@color/primary_text"
        android:textSize="16sp"
        android:visibility="gone" />

    <EditText
        android:id="@+id/etNumber"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_edit_text"
        android:hint="編號"
        android:importantForAutofill="no"
        android:inputType="number"
        android:padding="10dp"
        android:textSize="16sp"
        android:visibility="gone"
        tools:text="" />

    <Button
        android:id="@+id/btAdd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:background="@drawable/btn_primary_dark"
        android:text="@string/add"
        android:textColor="@color/white"
        android:textSize="16sp" />
</LinearLayout>
