<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".fragment.SettingFragment">

    <Button
        android:id="@+id/btCustomer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_fragment_setting_customer_information" />

    <Button
        android:id="@+id/btServiceItem"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_fragment_setting_service_item" />

    <Button
        android:id="@+id/btSetAppointmentFormat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_fragment_setting_set_appointment_format" />

    <Button
        android:id="@+id/btAbout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_fragment_setting_about" />
</LinearLayout>
