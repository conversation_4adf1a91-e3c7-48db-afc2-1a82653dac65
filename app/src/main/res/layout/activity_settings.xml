<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/btn_white"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="10dp"
    tools:context=".activity.SettingsActivity">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/app_activity_settings_set_up"
        android:textColor="@color/primary_text"
        android:textSize="20sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/app_activity_settings_set_appointment_key"
        android:textColor="@color/primary_text"
        android:textSize="16sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/etKeyWord"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:background="@drawable/bg_edit_text"
            android:gravity="center_horizontal"
            android:hint="請輸入關鍵字"
            android:importantForAutofill="no"
            android:inputType="text"
            android:padding="10dp"
            android:textColor="@color/primary_text"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btSaveKeyWord"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="2"
            android:background="@drawable/btn_primary_dark"
            android:padding="10dp"
            android:text="@string/app_activity_settings_set_up"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>


    <Button
        android:id="@+id/btShareFormat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/btn_primary_dark"
        android:padding="10dp"
        android:text="@string/app_activity_settings_share_appointment_format"
        android:textColor="@color/white"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btShareItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/btn_primary_dark"
        android:padding="10dp"
        android:text="@string/app_activity_settings_shared_services"
        android:textColor="@color/white"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btReplyToAppointment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/btn_primary_dark"
        android:padding="10dp"
        android:text="@string/app_activity_settings_reply_reservation"
        android:textColor="@color/white"
        android:textSize="16sp" />
</LinearLayout>
