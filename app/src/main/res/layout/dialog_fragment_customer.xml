<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/cardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:padding="0dp"
        app:cardBackgroundColor="@color/colorPrimaryDark"
        app:cardCornerRadius="5dp"
        app:cardElevation="5dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/colorPrimaryDark"
                android:gravity="center"
                android:padding="10dp"
                android:textStyle="bold"
                android:text="@string/app_dialog_fragment_customer_select_customers"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:padding="5dp"
                android:background="@color/colorPrimaryDark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>
