<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="5dp"
        android:padding="5dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="5dp"
        app:cardElevation="5dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_person_blue_24dp"
                android:visibility="visible" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_toStartOf="@+id/ivMore"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:padding="10dp">

                    <TextView
                        android:id="@+id/tvTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/primary_text"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="張小姐" />

                    <TextView
                        android:id="@+id/tvContent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/primary_text"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        tools:text="預約 2020/01/22 17:00 12345655 5555555555" />

                    <TextView
                        android:id="@+id/tvPostTime"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/secondary_text"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        tools:text="2020/01/22 17:00" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/ivMore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="10dp"
                    android:contentDescription="@null"
                    android:src="@drawable/ic_more_vert_black_24dp" />
            </RelativeLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>
