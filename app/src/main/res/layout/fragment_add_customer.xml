<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".fragment.customer.AddCustomerFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="gone"
            android:padding="20dp">

            <ImageView
                android:id="@+id/ivAvatar"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center_horizontal"
                android:contentDescription="@null"
                android:src="@drawable/ic_person_24dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/name"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/etName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/bg_edit_text"
                android:hint="請輸入名稱"
                android:importantForAutofill="no"
                android:inputType="text"
                android:padding="10dp"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/app_fragment_add_customer_nickname"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/etNickName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/bg_edit_text"
                android:hint="請輸入暱稱"
                android:inputType="text"
                android:padding="10dp"
                android:textSize="16sp"
                tools:text="" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/app_fragment_add_customer_telephone"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/etPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/bg_edit_text"
                android:hint="請輸入電話"
                android:inputType="phone"
                android:padding="10dp"
                android:textSize="16sp"
                tools:text="" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/app_fragment_add_customer_birthday"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvBirthday"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/bg_edit_text"
                android:hint="請選擇生日"
                android:importantForAutofill="no"
                android:padding="10dp"
                android:textSize="16sp"
                tools:text="" />

            <Button
                android:id="@+id/btAdd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/btn_primary_dark"
                android:text="@string/add"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <Button
                android:id="@+id/btAppointmentRecord"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/btn_primary_dark"
                android:text="@string/app_fragment_add_customer_view_appointment_records"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>
</ScrollView>
