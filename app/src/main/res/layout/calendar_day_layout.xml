<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:padding="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center">

        <!-- 日期文字 -->
        <TextView
            android:id="@+id/calendarDayText"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:gravity="center"
            android:textSize="16sp"
            android:textColor="@color/text_primary"
            android:textStyle="normal"
            android:background="@drawable/calendar_day_background_modern"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- 預約事件指示器 -->
    <View
        android:id="@+id/calendarDayIndicator"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="2dp"
        android:background="@drawable/calendar_event_indicator_modern"
        android:visibility="gone" />

</FrameLayout>
