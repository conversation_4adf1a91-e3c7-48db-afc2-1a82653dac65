<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="5dp"
        android:padding="5dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="5dp"
        app:cardElevation="5dp">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp">

            <ImageView
                android:id="@+id/ivAvatar"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_person_blue_24dp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_toEndOf="@+id/ivAvatar"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvNickName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/primary_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="Tina" />

                <TextView
                    android:id="@+id/tvName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/primary_text"
                    android:textSize="14sp"
                    android:textStyle="normal"
                    tools:text="張小姐" />

                <TextView
                    android:id="@+id/tvPhone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/secondary_text"
                    android:textSize="14sp"
                    android:textStyle="normal"
                    tools:text="0912345678" />
            </LinearLayout>

            <ImageView
                android:id="@+id/ivMore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="10dp"
                android:layout_centerVertical="true"
                android:contentDescription="@null"
                android:src="@drawable/ic_more_vert_black_24dp" />

        </RelativeLayout>

    </androidx.cardview.widget.CardView>


</LinearLayout>
