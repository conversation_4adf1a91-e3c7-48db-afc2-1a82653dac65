<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:padding="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center">

        <!-- 週視圖日期文字 -->
        <TextView
            android:id="@+id/calendarWeekDayText"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:gravity="center"
            android:textSize="18sp"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:background="@drawable/calendar_day_background_modern"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- 預約事件指示器 -->
    <View
        android:id="@+id/calendarWeekDayIndicator"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="2dp"
        android:background="@drawable/calendar_event_indicator_modern"
        android:visibility="gone" />

</FrameLayout>
