<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="2dp">

    <TextView
        android:id="@+id/calendarYearMonthText"
        android:layout_width="80dp"
        android:layout_height="60dp"
        android:gravity="center"
        android:textSize="12sp"
        android:textColor="@color/black"
        android:background="@drawable/calendar_month_background" />

    <!-- 月份事件指示器 -->
    <View
        android:id="@+id/calendarYearMonthIndicator"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_marginTop="2dp"
        android:background="@drawable/calendar_event_indicator"
        android:visibility="gone" />

</LinearLayout>
