<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ProgressBar
        android:id="@+id/progressBarMaterial"
        style="@android:style/Widget.Material.ProgressBar.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:layout_margin="5dp"
        android:indeterminateTint="@color/colorPrimary"
        android:indeterminateTintMode="src_atop"
        android:visibility="visible" />

<!--    <com.airbnb.lottie.LottieAnimationView-->
<!--        android:id="@+id/animation_view"-->
<!--        android:layout_width="@dimen/dp_40"-->
<!--        android:layout_height="@dimen/dp_40"-->
<!--        android:layout_centerInParent="true"-->
<!--        android:layout_gravity="center"-->
<!--        android:layout_margin="@dimen/dp_5"-->
<!--        android:scaleType="centerCrop"-->
<!--        android:visibility="visible"-->
<!--        app:lottie_autoPlay="true"-->
<!--        app:lottie_fileName="android_loading.json"-->
<!--        app:lottie_loop="true" />-->
</RelativeLayout>