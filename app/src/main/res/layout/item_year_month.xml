<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white"
    app:strokeWidth="1dp"
    app:strokeColor="@color/surface_variant">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="12dp">

            <TextView
                android:id="@+id/monthText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1月"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:gravity="center"
                android:fontFamily="sans-serif-medium" />

        </LinearLayout>

        <!-- 事件指示器 -->
        <View
            android:id="@+id/eventIndicator"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_gravity="top|end"
            android:layout_margin="8dp"
            android:background="@drawable/calendar_event_indicator_modern"
            android:visibility="gone" />

    </FrameLayout>

</com.google.android.material.card.MaterialCardView>
