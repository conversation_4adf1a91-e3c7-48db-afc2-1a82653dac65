<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".fragment.appointment.AppointmentListFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="0dp"
            android:paddingBottom="100dp">

            <!-- 日期顯示卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                android:visibility="gone"
                app:cardBackgroundColor="@color/colorPrimary"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="24dp">

                    <!-- 左側日期信息 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/tv_month_day"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="7月14日"
                                android:textSize="28sp"
                                android:textStyle="bold"
                                android:textColor="@color/white"
                                android:clickable="true"
                                android:focusable="true"
                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:padding="8dp" />

                            <TextView
                                android:id="@+id/tv_year"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="2025"
                                android:textSize="16sp"
                                android:textColor="@color/white"
                                android:alpha="0.8"
                                android:layout_marginStart="8dp"
                                android:clickable="true"
                                android:focusable="true"
                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:padding="4dp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_lunar"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="星期一"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:alpha="0.9"
                            android:layout_marginStart="12dp" />

                    </LinearLayout>

                    <!-- 右側操作按鈕 -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnToday"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="今天"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            app:strokeColor="@color/white"
                            app:backgroundTint="@android:color/transparent"
                            app:cornerRadius="18dp"
                            app:strokeWidth="1dp"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Compose 日曆視圖 -->
            <androidx.compose.ui.platform.ComposeView
                android:id="@+id/composeCalendarView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <!-- 預約列表卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="6dp"
                app:cardBackgroundColor="@color/white"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:orientation="vertical">

                    <!-- 預約列表標題 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="20dp"
                        android:paddingBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="今日預約"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chipAppointmentCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            app:chipBackgroundColor="@color/colorPrimary"
                            app:chipCornerRadius="24dp"
                            app:chipMinHeight="30dp"
                            app:textStartPadding="8dp"
                            app:textEndPadding="8dp" />

                    </LinearLayout>

                    <!-- 預約列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:paddingStart="12dp"
                        android:paddingEnd="12dp"
                        android:layout_marginTop="12dp"
                        android:paddingBottom="12dp"
                        android:clipToPadding="false" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- 現代化的 FAB -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fabAdd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="24dp"
        android:text="新增預約"
        android:textColor="@color/white"
        app:backgroundTint="@color/colorPrimary"
        app:icon="@android:drawable/ic_input_add"
        app:iconTint="@color/white"
        app:cornerRadius="28dp"
        app:elevation="8dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
