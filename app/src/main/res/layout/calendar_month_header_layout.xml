<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp">

    <!-- 星期標題 -->
    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="日"
        android:textSize="12sp"
        android:textColor="@color/colorPrimary"
        android:gravity="center" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="一"
        android:textSize="12sp"
        android:textColor="@color/black"
        android:gravity="center" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="二"
        android:textSize="12sp"
        android:textColor="@color/black"
        android:gravity="center" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="三"
        android:textSize="12sp"
        android:textColor="@color/black"
        android:gravity="center" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="四"
        android:textSize="12sp"
        android:textColor="@color/black"
        android:gravity="center" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="五"
        android:textSize="12sp"
        android:textColor="@color/black"
        android:gravity="center" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="六"
        android:textSize="12sp"
        android:textColor="@color/colorPrimary"
        android:gravity="center" />

</LinearLayout>
