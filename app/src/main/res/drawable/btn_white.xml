<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按壓時 -->
    <item android:state_pressed="true">
        <!--rectangle 長方形 可以畫出直角形，圓角形，弧形等-->
        <shape android:shape="rectangle">
            <!--設定圓角，當設置的圓角半徑很大，就可變成弧形邊了-->
            <corners android:radius="5dp" />
            <!--邊框-->
            <stroke android:width="1dp" android:color="@color/white_two" />
            <!--漸層顏色-->
            <gradient android:angle="0" android:startColor="@color/white_two" android:endColor="@color/white_two"  />
        </shape>
    </item>

    <!-- 預設時 -->
    <item android:color="@color/white_two">
        <shape android:shape="rectangle"  >
            <corners android:radius="5dp" />
            <stroke android:width="1dp" android:color="@color/white_two" />
            <gradient android:angle="0" android:startColor="@color/white_two" android:endColor="@color/white_two"  />
        </shape>
    </item>

</selector>