<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 選中狀態 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimaryYellow" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 當前月份 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorAccent" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 普通狀態 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="8dp" />
            <stroke android:width="1dp" android:color="@android:color/darker_gray" />
        </shape>
    </item>
    
</selector>
