<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 選中狀態 -->
    <item android:state_selected="true">
        <shape android:shape="oval">
            <solid android:color="@color/colorAccent" />
        </shape>
    </item>
    
    <!-- 今天 -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="@color/colorAccent" />
        </shape>
    </item>
    
    <!-- 普通狀態 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
    
</selector>
