<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/appointmentList"
    app:startDestination="@id/appointmentListFragment">

    <fragment
        android:id="@+id/addAppointmentFragment"
        android:name="com.one.appointment.fragment.appointment.AddAppointmentFragment"
        android:label="AddAppointmentFragment" />

    <fragment
        android:id="@+id/appointmentListFragment"
        android:name="com.one.appointment.fragment.appointment.AppointmentListFragment"
        android:label="AppointmentListFragment" >
        <action
            android:id="@+id/action_appointmentListFragment_to_addAppointmentFragment"
            app:destination="@id/addAppointmentFragment" />
    </fragment>

    <fragment
        android:id="@+id/customerListFragment"
        android:name="com.one.appointment.fragment.customer.CustomerListFragment"
        android:label="CustomerListFragment" />

    <fragment
        android:id="@+id/messageListFragment"
        android:name="com.one.appointment.fragment.message.MessageListFragment"
        android:label="MessageListFragment" />

    <fragment
        android:id="@+id/serviceItemListFragment"
        android:name="com.one.appointment.fragment.service.ServiceItemListFragment"
        android:label="ServiceItemListFragment" />
</navigation>