<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/nav_tool"
    app:startDestination="@id/addServiceFragment">

    <fragment
        android:id="@+id/addServiceFragment"
        android:name="com.one.appointment.fragment.service.AddServiceFragment"
        android:label="AddServiceFragment" />
    <fragment
        android:id="@+id/addAppointmentFragment"
        android:name="com.one.appointment.fragment.appointment.AddAppointmentFragment"
        android:label="AddAppointmentFragment" />
    <fragment
        android:id="@+id/customerListFragment"
        android:name="com.one.appointment.fragment.customer.CustomerListFragment"
        android:label="CustomerListFragment" />
    <fragment
        android:id="@+id/appointmentList"
        android:name="com.one.appointment.fragment.appointment.AppointmentListFragment"
        android:label="AppointmentListFragment" />
    <fragment
        android:id="@+id/appointmentRecordFragment"
        android:name="com.one.appointment.fragment.customer.AppointmentRecordFragment"
        android:label="AppointmentRecordFragment" />
    <fragment
        android:id="@+id/serviceItemListFragment"
        android:name="com.one.appointment.fragment.service.ServiceItemListFragment"
        android:label="ServiceItemListFragment" />

    <fragment
        android:id="@+id/addCustomerFragment"
        android:name="com.one.appointment.fragment.customer.AddCustomerFragment"
        android:label="AddCustomerFragment" />
</navigation>