{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:597887868345313031", "lastPropertyId": "7:2824698355086531740", "name": "Message", "properties": [{"id": "1:1151876959778064635", "name": "id", "type": 6, "flags": 1}, {"id": "2:4851484532004789377", "name": "title", "type": 9}, {"id": "3:1795973613623175935", "name": "content", "type": 9}, {"id": "4:8961594639110821961", "name": "postTime", "type": 6}, {"id": "5:5551069815863811992", "name": "packageName", "type": 9}, {"id": "7:2824698355086531740", "name": "imagePath", "type": 9}], "relations": []}, {"id": "3:6811686017643980910", "lastPropertyId": "10:3107318501076669396", "name": "Customer", "properties": [{"id": "1:2486343833361223357", "name": "id", "type": 6, "flags": 1}, {"id": "2:2098450917363588541", "name": "name", "type": 9}, {"id": "5:2806355476602921747", "name": "phoneNumber", "type": 9}, {"id": "7:4500688948759111383", "name": "gender", "type": 9}, {"id": "8:8367763756589142171", "name": "birthday", "type": 9}, {"id": "9:5434537952084498900", "name": "nick<PERSON><PERSON>", "type": 9}, {"id": "10:3107318501076669396", "name": "avatar<PERSON><PERSON><PERSON><PERSON>", "type": 9}], "relations": []}, {"id": "5:4632587987052156361", "lastPropertyId": "7:5267519905097294422", "name": "ServiceItem", "properties": [{"id": "1:3330006547694551265", "name": "id", "type": 6, "flags": 1}, {"id": "2:8415023999261730796", "name": "name", "type": 9}, {"id": "3:129126568230599993", "name": "price", "type": 5}, {"id": "4:3858807352915477752", "name": "serviceTime", "type": 5}, {"id": "5:426734920117295496", "name": "description", "type": 9}, {"id": "7:5267519905097294422", "name": "number", "type": 5}], "relations": []}, {"id": "7:2713783372070990728", "lastPropertyId": "3:8589833826579277549", "name": "Appointment", "properties": [{"id": "1:8863512042879366248", "name": "id", "type": 6, "flags": 1}, {"id": "2:3217105186624269195", "name": "time", "type": 6}, {"id": "3:8589833826579277549", "name": "customerId", "indexId": "4:7354104991740558009", "type": 11, "flags": 520, "relationTarget": "Customer"}], "relations": [{"id": "3:1021455608920025296", "name": "serviceItemList", "targetId": "5:4632587987052156361"}]}], "lastEntityId": "7:2713783372070990728", "lastIndexId": "4:7354104991740558009", "lastRelationId": "3:1021455608920025296", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [7716676992630377744, 4785173649207615429, 5837678487508112551], "retiredIndexUids": [5627035844472794749, 100536090296105979, 8905449563553424381], "retiredPropertyUids": [8496676163458614538, 3928664607163982748, 8238108587035963516, 237496494342036085, 7723715567531317556, 6378295774336744464, 5561199198610382334, 4085949501139787363, 3647911633569019487, 2254352663754286049, 6154610549385500386, 8372061163211505457, 6269432862906882549, 8578784714480462263], "retiredRelationUids": [4148580296616397176, 7668157704580694891], "version": 1}