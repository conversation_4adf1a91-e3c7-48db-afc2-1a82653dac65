# MaterialCalendarView 實現文件

## 概述
本文件記錄了將舊版 `haibin.calendarview` 替換為現代 `MaterialCalendarView` 的完整實現過程。

## 已完成的實現 ✅

### 1. 依賴項配置
```gradle
// app/build.gradle
dependencies {
    // material-calendarview - 替代 haibin calendarview
    implementation 'com.github.prolificinteractive:material-calendarview:2.0.1'
    // ThreeTen Backport - MaterialCalendarView 的依賴項
    implementation 'com.jakewharton.threetenabp:threetenabp:1.4.6'
}
```

### 2. Application 初始化
```kotlin
// App.kt
import com.jakewharton.threetenabp.AndroidThreeTen

@HiltAndroidApp
class App : MultiDexApplication() {
    override fun onCreate() {
        super.onCreate()
        // 初始化 ThreeTenABP (MaterialCalendarView 的依賴項)
        AndroidThreeTen.init(this)
        ObjectBox.init(this)
        // ... 其他初始化代碼
    }
}
```

### 3. Fragment 實現

#### A. 介面實現
```java
// AppointmentListFragment.java
public class AppointmentListFragment extends BaseFragment implements
        View.OnClickListener, OnDateSelectedListener, OnMonthChangedListener {
    
    private MaterialCalendarView mCalendarView;
    // ... 其他變數
}
```

#### B. Import 聲明
```java
import com.prolificinteractive.materialcalendarview.MaterialCalendarView;
import com.prolificinteractive.materialcalendarview.CalendarDay;
import com.prolificinteractive.materialcalendarview.OnDateSelectedListener;
import com.prolificinteractive.materialcalendarview.OnMonthChangedListener;
import org.threeten.bp.LocalDate;
```

#### C. 日曆設定方法
```java
/**
 * 設定 MaterialCalendarView
 */
private void setupMaterialCalendarView() {
    // 設定日期選擇監聽器
    mCalendarView.setOnDateChangedListener(this);
    
    // 設定月份變更監聽器
    mCalendarView.setOnMonthChangedListener(this);
    
    // 設定選擇模式為單選
    mCalendarView.setSelectionMode(MaterialCalendarView.SELECTION_MODE_SINGLE);
    
    // 設定當前日期
    CalendarDay today = CalendarDay.today();
    mCalendarView.setCurrentDate(today);
    mCalendarView.setSelectedDate(today);
    
    // 設定日期範圍（可選）
    LocalDate minDate = LocalDate.of(2020, 1, 1);
    LocalDate maxDate = LocalDate.of(2030, 12, 31);
    
    mCalendarView.state().edit()
            .setMinimumDate(CalendarDay.from(minDate))
            .setMaximumDate(CalendarDay.from(maxDate))
            .commit();
}
```

#### D. 日期顯示更新
```java
/**
 * 更新日期顯示
 */
private void updateDateDisplay(CalendarDay date) {
    LocalDate localDate = date.getDate();
    
    mYear = localDate.getYear();
    mMonth = localDate.getMonthValue(); // LocalDate.getMonthValue() 是 1-based
    mDay = localDate.getDayOfMonth();
    
    mTextYear.setText(String.valueOf(mYear));
    mTextMonthDay.setText(mMonth + "月" + mDay + "日");
    mTextLunar.setText("今日");
    mTextLunar.setVisibility(View.VISIBLE);
}
```

#### E. 回調方法實現
```java
// 日期選擇回調
@Override
public void onDateSelected(MaterialCalendarView widget, CalendarDay date, boolean selected) {
    if (selected) {
        // 更新日期顯示
        updateDateDisplay(date);
        
        // 顯示相關 UI 元素
        mTextLunar.setVisibility(View.VISIBLE);
        mTextYear.setVisibility(View.VISIBLE);
        fabAdd.setVisibility(View.VISIBLE);
        mActivity.binding.bottomNavigation.setVisibility(View.VISIBLE);
        
        // 重新載入該日期的預約資料
        initData();
    }
}

// 月份變更回調
@Override
public void onMonthChanged(MaterialCalendarView widget, CalendarDay date) {
    LocalDate localDate = date.getDate();
    
    int year = localDate.getYear();
    int month = localDate.getMonthValue();
    
    LogUtil.d("Month changed to: " + year + "/" + month);
    
    // 可以在這裡重新標記該月份的預約日期
    markAppointmentDates();
}
```

#### F. 點擊事件處理
```java
// 設定點擊事件
mTextMonthDay.setOnClickListener(v -> {
    // 可以實現月份選擇功能
    // MaterialCalendarView 沒有直接的年份選擇，但可以通過其他方式實現
});

view.findViewById(R.id.fl_current).setOnClickListener(v -> {
    // 滾動到今天
    CalendarDay today = CalendarDay.today();
    mCalendarView.setCurrentDate(today);
    mCalendarView.setSelectedDate(today);
});
```

## API 對照表

### 舊版 haibin.calendarview vs 新版 MaterialCalendarView

| 功能 | 舊版 API | 新版 API |
|------|----------|----------|
| 獲取當前年份 | `mCalendarView.getCurYear()` | `CalendarDay.today().getDate().getYear()` |
| 獲取當前月份 | `mCalendarView.getCurMonth()` | `CalendarDay.today().getDate().getMonthValue()` |
| 獲取當前日期 | `mCalendarView.getCurDay()` | `CalendarDay.today().getDate().getDayOfMonth()` |
| 設定日期選擇監聽器 | `setOnCalendarSelectListener()` | `setOnDateChangedListener()` |
| 設定年份變更監聽器 | `setOnYearChangeListener()` | `setOnMonthChangedListener()` |
| 滾動到當前日期 | `scrollToCurrent()` | `setCurrentDate(CalendarDay.today())` |
| 設定日期標記 | `setSchemeDate(map)` | 使用 `DayViewDecorator` |

## 主要差異

### 1. 日期類型
- **舊版**: 使用 `java.util.Calendar` 和自定義 `Calendar` 類別
- **新版**: 使用 `org.threeten.bp.LocalDate` (ThreeTen Backport)

### 2. 回調介面
- **舊版**: `OnCalendarSelectListener`, `OnYearChangeListener`
- **新版**: `OnDateSelectedListener`, `OnMonthChangedListener`

### 3. 日期標記
- **舊版**: 使用 `setSchemeDate()` 方法和 Map
- **新版**: 使用 `DayViewDecorator` 系統

## 待實現功能 🔄

### 1. 日期標記功能
```java
/**
 * 標記有預約的日期
 * TODO: 實現 DayViewDecorator 來標記特殊日期
 */
private void markAppointmentDates() {
    // 查詢資料庫中的預約資料
    List<Appointment> appointments = mAppointmentBox.getAll();
    
    // 創建 DayViewDecorator 來標記有預約的日期
    // 例如：
    // mCalendarView.addDecorator(new AppointmentDecorator(appointmentDates));
}
```

### 2. 年份選擇功能
MaterialCalendarView 沒有內建的年份選擇器，可以考慮：
- 使用 DatePickerDialog
- 自定義年份選擇對話框
- 使用第三方年份選擇器

### 3. 農曆顯示
如果需要農曆功能，需要：
- 整合農曆計算庫
- 自定義 DayViewDecorator 顯示農曆資訊

## 優勢

### MaterialCalendarView 的優點
1. **現代化設計**: 遵循 Material Design 規範
2. **活躍維護**: 持續更新和維護
3. **良好文檔**: 完整的 API 文檔和範例
4. **靈活性**: 高度可自定義的外觀和行為
5. **穩定性**: 成熟的庫，bug 較少

### 相容性
- **最低 API 級別**: API 14+
- **AndroidX 相容**: 完全支援 AndroidX
- **Kotlin 友好**: 支援 Kotlin 和 Java

## 測試建議

### 1. 功能測試
- [ ] 日期選擇功能
- [ ] 月份切換功能
- [ ] 今日按鈕功能
- [ ] 日期範圍限制
- [ ] 預約資料載入

### 2. UI 測試
- [ ] 日期顯示格式
- [ ] 響應式佈局
- [ ] 主題適配
- [ ] 動畫效果

### 3. 性能測試
- [ ] 大量資料載入
- [ ] 記憶體使用
- [ ] 滑動流暢度

## 結論

MaterialCalendarView 成功替代了舊版的 haibin.calendarview，提供了：
- ✅ 現代化的 UI 設計
- ✅ 穩定的 API
- ✅ 良好的維護狀態
- ✅ 完整的功能實現

雖然在某些特定功能（如農曆顯示、年份選擇）上需要額外的實現工作，但整體上提供了更好的開發體驗和用戶體驗。
