// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

    ext {
        nav_version = "2.4.2"
        kotlin_version = "2.0.0"
        viewbindingpropertydelegate_version = "1.5.6"
        permissionsdispatcher_version = "4.9.2"
        material_version = '1.11.0'
    }

    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        gradlePluginPortal()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.10.1'

        classpath "io.objectbox:objectbox-gradle-plugin:3.7.0"
        classpath 'com.google.gms:google-services:4.3.13'  // Google Services plugin

        // Add the Crashlytics Gradle plugin.
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.1'

        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jetbrains.kotlin:compose-compiler-gradle-plugin:$kotlin_version"
        classpath 'com.google.firebase:firebase-appdistribution-gradle:3.0.2'

        classpath 'com.google.dagger:hilt-android-gradle-plugin:2.42'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.1'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.5.2'

        // Add the dependency for the App Distribution Gradle plugin
        classpath 'com.google.firebase:firebase-appdistribution-gradle:3.0.3'
    }
}

plugins {
//    id 'com.android.application' version '8.9.1' apply false
//    id 'com.android.library' version '8.9.1' apply false
    id 'org.jetbrains.kotlin.android' version '2.0.0' apply false
    id "org.jlleitschuh.gradle.ktlint" version "11.0.0"
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url 'https://repo1.maven.org/maven2' }
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots/' }
        gradlePluginPortal()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
