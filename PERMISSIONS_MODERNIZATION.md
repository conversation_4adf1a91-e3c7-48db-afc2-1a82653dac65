# Android 權限處理現代化文件

## 概述
本文件記錄了將已棄用的 `permissions.dispatcher` 庫替換為現代 Android Activity Result API 的完整實現過程。

## 問題背景

### 舊版 permissions.dispatcher 的問題
1. **已停止維護**: permissions.dispatcher 庫已經不再維護
2. **相容性問題**: 與新版 Android Gradle Plugin 和 Kotlin 版本不相容
3. **過時的 API**: 使用已棄用的 `onRequestPermissionsResult` 方法
4. **註解處理器問題**: 依賴 kapt 註解處理，增加編譯時間

### 現代解決方案
使用 Android Activity Result API，這是 Google 推薦的現代權限處理方式。

## 實現詳情

### 1. 移除舊依賴項

#### 移除的依賴項
```gradle
// app/build.gradle - 已移除
// implementation "com.github.permissions-dispatcher:permissionsdispatcher:$permissionsdispatcher_version"
// kapt "com.github.permissions-dispatcher:permissionsdispatcher-processor:$permissionsdispatcher_version"
```

### 2. 創建現代權限處理基類

#### ModernPermissionActivity.kt
```kotlin
/**
 * 現代權限處理基類
 * 使用 Android Activity Result API 替代已棄用的 permissions.dispatcher
 */
abstract class ModernPermissionActivity : BaseActivity() {
    
    // 權限請求啟動器
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        handlePermissionResult(permissions)
    }
    
    // 單個權限請求啟動器
    private val singlePermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        handleSinglePermissionResult(isGranted)
    }
    
    // ... 其他實現
}
```

#### 主要功能
1. **多權限請求**: 支援同時請求多個權限
2. **單權限請求**: 支援請求單個權限
3. **權限說明**: 自動處理權限說明對話框
4. **永久拒絕檢測**: 檢測用戶是否永久拒絕權限
5. **回調系統**: 完整的權限結果回調

### 3. 更新 PermissionActivity.kt

#### 向後相容設計
```kotlin
/**
 * 權限處理活動基類
 * 已更新為使用現代 Android Activity Result API
 * 替代已棄用的 permissions.dispatcher
 */
abstract class PermissionActivity : ModernPermissionActivity() {
    
    // 保持向後相容的方法
    open fun showPermissionsAllow() {
        LogUtil.i("permissions: 用戶允許權限")
        isAllowPermissions = true
    }
    
    open fun onShowRationale(onProceed: () -> Unit) {
        LogUtil.i("permissions: 向用戶說明為什麼需要這些權限")
        onProceed()
    }
    
    // ... 其他向後相容方法
}
```

### 4. 清理 BaseActivity.kt

#### 移除的舊代碼
- 移除所有 `@NeedsPermission` 註解
- 移除所有 `@OnShowRationale` 註解
- 移除所有 `@OnPermissionDenied` 註解
- 移除所有 `@OnNeverAskAgain` 註解
- 移除 `@RuntimePermissions` 類別註解

#### 保留的現代代碼
```kotlin
// BaseActivity.kt 中保留的現代權限處理擴展函數
fun AppCompatActivity.askPermission(
    permission: String,
    onPermissionGranted: (() -> Unit)? = null,
    onShouldShowRequestRational: ((() -> Unit) -> Unit)? = null,
    onPermissionDenied: (() -> Unit)? = null
) {
    val requestPermission = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) onPermissionGranted?.invoke()
        else onPermissionDenied?.invoke()
    }
    // ... 實現邏輯
}
```

## API 對照表

### 舊版 permissions.dispatcher vs 新版 Activity Result API

| 功能 | 舊版 API | 新版 API |
|------|----------|----------|
| 權限請求 | `@NeedsPermission` | `registerForActivityResult(RequestPermission())` |
| 多權限請求 | 多個 `@NeedsPermission` | `registerForActivityResult(RequestMultiplePermissions())` |
| 權限說明 | `@OnShowRationale` | `shouldShowRequestPermissionRationale()` |
| 權限拒絕 | `@OnPermissionDenied` | 回調函數中處理 |
| 永久拒絕 | `@OnNeverAskAgain` | 檢查 `shouldShowRequestPermissionRationale()` |
| 結果處理 | `onRequestPermissionsResult()` | 啟動器回調 |

## 使用方式

### 1. 基本權限請求
```kotlin
class MyActivity : PermissionActivity() {
    
    override fun getRequiredPermissions(): List<String> {
        return listOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA
        )
    }
    
    override fun onPermissionsGranted() {
        super.onPermissionsGranted()
        // 所有權限都已授予，可以執行相關操作
    }
    
    override fun onPermissionsDenied(deniedPermissions: List<String>) {
        super.onPermissionsDenied(deniedPermissions)
        // 處理權限被拒絕的情況
    }
}
```

### 2. 單個權限請求
```kotlin
// 請求單個權限
requestSinglePermission(Manifest.permission.CAMERA)
```

### 3. 檢查權限狀態
```kotlin
// 檢查是否有所有必需的權限
if (hasAllRequiredPermissions()) {
    // 執行需要權限的操作
} else {
    // 重新請求權限
    recheckPermissions()
}
```

## 優勢

### 現代 Activity Result API 的優點
1. **官方支援**: Google 官方推薦的現代方式
2. **類型安全**: 編譯時類型檢查
3. **簡潔代碼**: 減少樣板代碼
4. **更好的測試**: 更容易進行單元測試
5. **生命週期感知**: 自動處理 Activity 生命週期
6. **記憶體安全**: 避免記憶體洩漏

### 相容性
- **最低 API 級別**: API 16+
- **AndroidX 相容**: 完全支援 AndroidX
- **Kotlin 友好**: 原生支援 Kotlin
- **向後相容**: 保持現有 API 介面不變

## 遷移指南

### 對於現有代碼
1. **無需修改**: 現有使用 `PermissionActivity` 的代碼無需修改
2. **方法保持**: 所有原有的回調方法都保持不變
3. **行為一致**: 權限處理行為與之前完全一致

### 對於新代碼
1. **使用新 API**: 建議新代碼直接使用 `ModernPermissionActivity`
2. **更靈活**: 可以自定義權限處理邏輯
3. **更強大**: 支援更複雜的權限處理場景

## 測試建議

### 1. 功能測試
- [ ] 權限授予流程
- [ ] 權限拒絕流程
- [ ] 權限說明顯示
- [ ] 永久拒絕處理
- [ ] 多權限請求

### 2. 邊界測試
- [ ] Activity 重建時的權限狀態
- [ ] 系統設定中手動修改權限
- [ ] 低記憶體情況下的權限處理

### 3. 相容性測試
- [ ] 不同 Android 版本
- [ ] 不同設備製造商
- [ ] 不同螢幕尺寸

## 故障排除

### 常見問題
1. **權限請求無響應**: 檢查是否在 `onCreate()` 之前註冊啟動器
2. **回調未觸發**: 確保 Activity 沒有被銷毀
3. **權限狀態不正確**: 檢查 AndroidManifest.xml 中的權限聲明

### 除錯技巧
1. **啟用日誌**: 使用 `LogUtil` 查看權限處理流程
2. **檢查權限狀態**: 使用 `hasPermission()` 方法檢查當前狀態
3. **測試不同場景**: 測試授予、拒絕、永久拒絕等場景

## 結論

成功將 `permissions.dispatcher` 替換為現代 Android Activity Result API，實現了：
- ✅ 移除已棄用的依賴項
- ✅ 使用現代 Android API
- ✅ 保持向後相容性
- ✅ 提供更好的開發體驗
- ✅ 支援更複雜的權限處理場景

這次現代化不僅解決了編譯問題，還為未來的開發提供了更穩定、更靈活的權限處理基礎。
