apply plugin: 'com.android.library'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-parcelize'

android {
    namespace 'com.one.core'
    compileSdk 35

    defaultConfig {
        minSdk 21
        targetSdk 35

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    viewBinding {
        enabled = true
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    implementation 'androidx.appcompat:appcompat:1.6.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'



    api 'androidx.recyclerview:recyclerview:1.3.1'
    api 'androidx.cardview:cardview:1.0.0'
    // Google billing
    api 'com.android.billingclient:billing:6.0.1'
    // ads
//    implementation 'com.google.android.gms:play-services-ads:19.1.0'
    // gson
    api 'com.google.code.gson:gson:2.9.0'

    // Import the BoM for the Firebase platform
    api platform('com.google.firebase:firebase-bom:30.2.0')

    // Declare the dependencies for the Remote Config and Analytics libraries
    // When using the BoM, you don't specify versions in Firebase library dependencies
    api 'com.google.firebase:firebase-config'
    api 'com.google.firebase:firebase-analytics'


    // add the Firebase SDK for Google Analytics
//    api 'com.google.firebase:firebase-analytics:21.0.0'
    // Add the SDK for Firebase Cloud Messaging
    api 'com.google.firebase:firebase-messaging:23.2.1'
    // Add the SDKs for any other Firebase products you want to use in your app
    // For example, to use Firebase Authentication and Cloud Firestore
    api 'com.google.firebase:firebase-auth:22.1.1'
    api 'com.google.firebase:firebase-firestore:24.7.1'

    // Add the In-App Messaging and Analytics dependencies:
    api 'com.google.firebase:firebase-inappmessaging-display:20.3.3'
    // Add the Firebase SDK for Google Analytics
//    api 'com.google.firebase:firebase-analytics:21.0.0'
    // Add the Firebase SDK for Crashlytics
    api 'com.google.firebase:firebase-crashlytics:18.4.1'

    // retrofit
    api 'com.squareup.retrofit2:retrofit:2.9.0'
    api 'com.squareup.retrofit2:converter-gson:2.9.0'

    api 'com.google.android.material:material:1.9.0'

    //material-dialogs
    api 'com.afollestad.material-dialogs:core:3.3.0'
    api 'com.afollestad.material-dialogs:input:3.1.1'
    api 'com.afollestad.material-dialogs:files:3.1.1'
    api 'com.afollestad.material-dialogs:color:3.1.1'
    api 'com.afollestad.material-dialogs:datetime:3.1.1'
    api 'com.afollestad.material-dialogs:bottomsheets:3.1.1'
    api 'com.afollestad.material-dialogs:lifecycle:3.1.1'


    // 權限 permissionsdispatcher
    implementation "com.github.permissions-dispatcher:permissionsdispatcher:$permissionsdispatcher_version"
    kapt "com.github.permissions-dispatcher:permissionsdispatcher-processor:$permissionsdispatcher_version"


    //****************************************************************************//
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.5.1'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.5.1'
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4")
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4"

    implementation 'androidx.hilt:hilt-navigation-fragment:1.0.0'
//    // reflection-free flavor
//    api 'com.github.kirich1409:viewbindingpropertydelegate-noreflection:1.5.6'

    // Google ads
    implementation 'com.google.android.gms:play-services-ads:21.2.0'

    // FB ads
    implementation 'androidx.annotation:annotation:1.5.0'
    implementation 'com.facebook.android:audience-network-sdk:6.11.0'
}