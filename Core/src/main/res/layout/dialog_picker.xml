<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <NumberPicker
            android:id="@+id/numberPicker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#292929"
            android:textSize="20sp"
            android:theme="@style/DefaultNumberPickerTheme" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/very_light_pink" />

        <TextView
            android:id="@+id/tvDone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:gravity="center_horizontal"
            android:text="Done"
            android:textColor="@color/greyish_brown"
            android:textSize="16sp" />

    </LinearLayout>

</RelativeLayout>
