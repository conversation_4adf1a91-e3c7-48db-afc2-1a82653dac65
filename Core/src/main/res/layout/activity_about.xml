<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/rectangle_white_r10"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="10dp">

    <TextView
        android:id="@+id/tvDesc"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="5dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="@string/about"
        android:textColor="@color/primary_text"
        android:textSize="20sp" />

    <RelativeLayout
        android:id="@+id/rltComment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/btn_primary_dark">

        <TextView
            android:id="@+id/tvTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_margin="10dp"
            android:text="@string/app_activity_about_ratings_and_comments"
            android:textColor="#ffffffff"
            android:textSize="18sp" />

        <ImageView
            android:id="@+id/ibComment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:contentDescription="@null" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rltIssueResponses"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@color/colorPrimaryDark"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_margin="10dp"
            android:text="@string/report_problem"
            android:textColor="#ffffffff"
            android:textSize="18sp" />

        <ImageView
            android:id="@+id/ibIssueResponses"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:contentDescription="@null" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rltVersion"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/btn_primary_dark"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/tvVersion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp"
            android:text="@string/check_for_version_updates"
            android:textColor="#ffffffff"
            android:textSize="18sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/current_version_number"
                android:textColor="#ffffffff"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvVersionNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#ffffffff"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tvVersion"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:text="@string/current_version_name"
                android:textColor="#ffffffff"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvVersionName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#ffffffff"
                android:textSize="16sp" />
        </LinearLayout>
    </RelativeLayout>

</LinearLayout>
