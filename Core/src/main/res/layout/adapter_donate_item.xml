<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_margin="10dp"
    android:background="@drawable/btn_primary_dark"
    android:layout_gravity="center_horizontal"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="10dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        tools:text="贊助 - 鼓勵軟體開發與更新"
        android:textColor="#ffffffff"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:gravity="center"
        tools:text="$30"
        android:textColor="#ffffffff"
        android:textSize="18sp" />
</LinearLayout>