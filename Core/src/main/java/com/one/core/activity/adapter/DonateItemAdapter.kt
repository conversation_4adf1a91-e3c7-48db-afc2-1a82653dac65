package com.one.core.activity.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.ProductDetails
import com.one.core.R

class DonateItemAdapter(private val onClickListener: View.OnClickListener) :
    RecyclerView.Adapter<DonateItemAdapter.ViewHolder>() {
    private var mList: ArrayList<ProductDetails> = ArrayList()

    fun setList(list: ArrayList<ProductDetails>) {
        mList = list
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): ViewHolder {
        val v = LayoutInflater.from(viewGroup.context)
            .inflate(R.layout.adapter_donate_item, viewGroup, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, i: Int) {
        val productDetails = mList[i]
        viewHolder.tvTitle.text = mList[i].title
        if (mList[i].productType == BillingClient.ProductType.INAPP) {
            viewHolder.tvPrice.text = productDetails.oneTimePurchaseOfferDetails?.formattedPrice ?: ""
        } else {
            viewHolder.tvPrice.text =
                productDetails.subscriptionOfferDetails?.get(0)?.pricingPhases?.pricingPhaseList?.get(0)?.formattedPrice ?: ""
        }
        viewHolder.itemView.tag = productDetails
        viewHolder.itemView.setOnClickListener(onClickListener)
    }

    override fun getItemCount(): Int {
        return mList.size
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var tvTitle: TextView
        var tvPrice: TextView

        init {
            tvTitle = itemView.findViewById(R.id.tvTitle)
            tvPrice = itemView.findViewById(R.id.tvPrice)
        }
    }
}