package com.one.core.activity

import android.os.Bundle
import android.view.Window
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.ProductDetails
import com.google.gson.Gson
import com.one.core.activity.adapter.DonateItemAdapter
import com.one.core.billing.BillingClientLifecycle
import com.one.core.databinding.ActivityDonateBinding
import com.one.core.firebase.RemoteConfigUtil
import com.one.core.iab.SkuIdList
import com.one.core.util.LogUtil
import com.one.core.viewmodel.BillingModelFactory
import com.one.core.viewmodel.BillingViewModel

class DonateActivity : AppCompatActivity() {
    private lateinit var billingClientLifecycle: BillingClientLifecycle
    private lateinit var binding: ActivityDonateBinding
    private lateinit var billingViewModel: BillingViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 不顯示label 要在setContentView之前呼叫
        requestWindowFeature(Window.FEATURE_NO_TITLE)

        binding = ActivityDonateBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Billing APIs are all handled in the this lifecycle observer.
        billingClientLifecycle = BillingClientLifecycle.getInstance(applicationContext)
        lifecycle.addObserver(billingClientLifecycle)

        billingViewModel = ViewModelProvider(
            this,
            BillingModelFactory(application, billingClientLifecycle)
        )[BillingViewModel::class.java]

        fetchConfig()
    }

    private fun initRecyclerView(skuIdList: SkuIdList) {
        binding.recyclerView.setHasFixedSize(true)
        binding.recyclerView.layoutManager = GridLayoutManager(this, 1)

        val donateItemAdapter = DonateItemAdapter {
            val productDetails = it.tag as ProductDetails
            if (productDetails.productType == BillingClient.ProductType.INAPP) {
                billingViewModel.buy(productDetails.productId)
            } else {
                billingViewModel.buy("", productDetails.productId, false)
            }
        }

        binding.recyclerView.adapter = donateItemAdapter

        billingClientLifecycle.queryProductDetails(
            skuIdList.inappItemList,
            BillingClient.ProductType.INAPP
        )

        billingClientLifecycle.queryProductDetails(
            skuIdList.subsItemList,
            BillingClient.ProductType.SUBS
        )

        billingClientLifecycle.purchasesFinished.observe(this) {
            Toast.makeText(this, "感謝贊助!", Toast.LENGTH_SHORT).show()
        }

        // Launch the billing flow when the user clicks a button to buy something.
        billingViewModel.buyEvent.observe(this) {
            if (it != null) {
                billingClientLifecycle.launchBillingFlow(this, it)
            }
        }

        val productList: ArrayList<ProductDetails> = ArrayList()
        val productsInAppWithProductDetails = billingClientLifecycle.productsInAppWithProductDetails
        productsInAppWithProductDetails.observe(this) {
            productList.clear()
            productList.addAll(it.values.toList())
            var hashSet = LinkedHashSet(productList)
            var listWithoutDuplicates = ArrayList(hashSet)
            donateItemAdapter.setList(listWithoutDuplicates)

            val productsSubsWithProductDetails =
                billingClientLifecycle.productsSubsWithProductDetails
            productsSubsWithProductDetails.observe(this) { itSubs ->
                productList.addAll(itSubs.values.toList())
                hashSet = LinkedHashSet(productList)
                listWithoutDuplicates = ArrayList(hashSet)
                donateItemAdapter.setList(listWithoutDuplicates)
            }
        }
    }

    private fun fetchConfig() {
        RemoteConfigUtil.fetchConfig(this) { task, firebaseRemoteConfig ->
            if (task.isSuccessful) {
                try {
                    val billingItemList = firebaseRemoteConfig.getString("billingItemList")
                    LogUtil.d("billingItemList : $billingItemList")
                    val gson = Gson()
                    val skuIdList = gson.fromJson(billingItemList, SkuIdList::class.java)
                    if (skuIdList == null) {
                        Toast.makeText(this@DonateActivity, "無可購買商品", Toast.LENGTH_SHORT).show()
                        finish()
                        return@fetchConfig
                    }
                    initRecyclerView(skuIdList)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            } else {
                // 無法取得 Remote Config
                LogUtil.e("無法取得 Remote Config")
                Toast.makeText(
                    this@DonateActivity,
                    "無可購買商品",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }
}