package com.one.core.activity

import android.content.Intent
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.os.Bundle
import android.view.WindowManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.one.core.util.LogUtil


abstract class BaseActivity : AppCompatActivity() {
    private var mIsCheckPermission = false

    //    private NetworkCallbackImpl networkCallback;
    private val connectivityManager: ConnectivityManager? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 避免小鍵盤跳出
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        val bundle = intent.extras
        initParams(bundle)
        //        registerReceiver();
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        // 避免小鍵盤跳出
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        val bundle = intent.extras
        initParams(bundle)
    }

    public override fun onResume() {
        super.onResume()
        LogUtil.d("onResume")
        //        if (mIsCheckPermission) {
//            BaseActivityPermissionsDispatcher.AllowsPermissionAccessLocationWithPermissionCheck(this);
//        }
    }

    val appCompatActivity: AppCompatActivity
        //    private void registerReceiver() {
        get() = this

    override fun onDestroy() {
        super.onDestroy()
        LogUtil.d()
        //        connectivityManager.unregisterNetworkCallback(networkCallback);
    }

    open fun hasPermission(isCheckPermission: Boolean) {
        mIsCheckPermission = isCheckPermission
    }

    public override fun onPause() {
        super.onPause()
        LogUtil.i("onPause")
    }

    /**
     * [初始化參數]
     *
     * @param bundle 參數
     */
    abstract fun initParams(bundle: Bundle?)
    val activity: AppCompatActivity
        get() = this

    /**
     * [頁面跳轉]
     *
     * @param clz Class
     */
    fun startActivity(clz: Class<*>?) {
        startActivity(Intent(this@BaseActivity, clz))
    }

    /**
     * [攜帶數據的頁面跳轉]
     *
     * @param clz    Class
     * @param bundle 參數
     */
    fun startActivity(clz: Class<*>?, bundle: Bundle?) {
        val intent = Intent()
        intent.setClass(this, clz!!)
        if (bundle != null) {
            intent.putExtras(bundle)
        }
        startActivity(intent)
    }

    //    @Override
    //    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
    //        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    //        BaseActivityPermissionsDispatcher.onRequestPermissionsResult(this, requestCode, grantResults);
    //    }
    // 舊的 permissions.dispatcher 代碼已移除
    // 現在使用現代的 Activity Result API 處理權限





    // 用戶勾選了“不再提醒”時調用（可選）


    fun AppCompatActivity.askPermission(
        permission: String,
        onPermissionGranted: (() -> Unit)? = null,
        onShouldShowRequestRational: ((() -> Unit) -> Unit)? = null,
        onPermissionDenied: (() -> Unit)? = null
    ) {
        val requestPermission = registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted: Boolean ->
            if (isGranted) onPermissionGranted?.invoke()
            else onPermissionDenied?.invoke()
        }

        if (hasPermission(permission))
            onPermissionGranted?.invoke()
        else if (shouldShowRequestPermissionRationale(permission))
            onShouldShowRequestRational?.invoke { requestPermission.launch(permission) }
        else
            requestPermission.launch(permission)
    }

    private fun hasPermission(permission: String): Boolean {
        val result =
            ContextCompat.checkSelfPermission(this, permission)
        return result == PackageManager.PERMISSION_GRANTED
    }
}