package com.one.core.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.Window
import android.widget.Toast
import androidx.core.content.pm.PackageInfoCompat
import com.afollestad.materialdialogs.MaterialDialog
import com.one.core.R
import com.one.core.databinding.ActivityAboutBinding
import com.one.core.firebase.RemoteConfigUtil
import com.one.core.util.LogUtil
import java.io.ByteArrayInputStream
import java.security.cert.CertificateException
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import javax.security.auth.x500.X500Principal

class AboutActivity : BaseActivity() {

    private var argVersionCode = 0
    private lateinit var binding: ActivityAboutBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 不顯示label 要在setContentView之前呼叫
        requestWindowFeature(Window.FEATURE_NO_TITLE)

        binding = ActivityAboutBinding.inflate(layoutInflater)
        setContentView(binding.root)

        try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val verCode = PackageInfoCompat.getLongVersionCode(packageInfo).toInt()
            binding.tvVersionName.text =
                getString(R.string.version, packageInfo.versionName.toString(), verCode)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        setListener()
    }

    override fun initParams(bundle: Bundle?) {
        if (bundle != null) {
            argVersionCode = bundle.getInt(ARG_VERSION_CODE, 0)
        }
    }

    private fun setListener() {
        binding.rltVersion.setOnLongClickListener {
            try {
                isDebuggable(this@AboutActivity)
                val info = packageManager!!.getPackageInfo(packageName!!, 0)
                Toast.makeText(
                    this@AboutActivity,
                    getString(R.string.version_no) + info.versionCode + getString(R.string.version_name) + info.versionName
                            + getString(R.string.is_debug) + isDebuggable(this@AboutActivity),
                    Toast.LENGTH_LONG
                ).show()
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
            }
            false
        }
        binding.rltVersion.setOnClickListener { checkVersionConfig() }
        binding.rltComment.setOnClickListener { // Open app with Google Play app
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName"))
            startActivity(intent)
        }
        binding.rltIssueResponses.setOnClickListener {
            val uri = Uri.parse("https://www.facebook.com/Ace-Producer-2150781041902568")
            val i = Intent(Intent.ACTION_VIEW, uri)
            startActivity(i)
        }
    }

    // 檢查版本
    private fun checkVersionConfig() {
        RemoteConfigUtil.fetchConfig(this) { task, firebaseRemoteConfig ->
            if (task.isSuccessful) {
                // get value from remote config
                val isForceUpdate = firebaseRemoteConfig.getBoolean("force_update")
                val versionCode = firebaseRemoteConfig.getString("version_code")
                val apkURL = firebaseRemoteConfig.getString("apk_url")
                if (!TextUtils.isEmpty(versionCode) && versionCode.toInt() > argVersionCode) {
                    val dialog =
                        MaterialDialog(this@AboutActivity, MaterialDialog.DEFAULT_BEHAVIOR)
                    dialog.title(null, "檢查版本更新")
                    dialog.message(null, "已有新版本是否進行更新?", null)
                    dialog.positiveButton(null, "是") {
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(apkURL))
                        startActivity(intent)
                    }
                    dialog.negativeButton(null, "否") {
                        if (isForceUpdate) {
                            finish()
                        } else {
                            LogUtil.d("否")
                        }
                    }
                    dialog.show()
                } else {
                    LogUtil.d("不須更新")
                    Toast.makeText(
                        appCompatActivity,
                        getString(R.string.no_updates_available),
                        Toast.LENGTH_SHORT
                    ).show()
                }
            } else {
                // 無法取得 Remote Config
                LogUtil.e("無法取得 Remote Config")
                Toast.makeText(
                    appCompatActivity,
                    getString(R.string.no_updates_available),
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    private fun isDebuggable(ctx: Context): Boolean {
        var debuggable = false
        try {
            @SuppressLint("PackageManagerGetSignatures") val packageInfo =
                ctx.packageManager.getPackageInfo(ctx.packageName, PackageManager.GET_SIGNATURES)
            val signatures = packageInfo.signatures
            val cf = CertificateFactory.getInstance("X.509")
            signatures?.let { sigs ->
                for (signature in sigs) {
                    val stream = ByteArrayInputStream(signature.toByteArray())
                    val cert = cf.generateCertificate(stream) as X509Certificate
                    debuggable = cert.subjectX500Principal == DEBUG_DN
                    if (debuggable) break
                }
            }
        } catch (e: PackageManager.NameNotFoundException) {
            // debuggable variable will remain false
            e.printStackTrace()
        } catch (e: CertificateException) {
            e.printStackTrace()
        }
        return debuggable
    }

    companion object {
        var ARG_VERSION_CODE = "VERSION_CODE"
        private val DEBUG_DN = X500Principal("CN=Android Debug,O=Android,C=US")
    }
}