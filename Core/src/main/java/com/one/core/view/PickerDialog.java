package com.one.core.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.NumberPicker;
import android.widget.TextView;

import com.one.core.R;


public class PickerDialog extends Dialog {

    private final Context mContext;
    private String[] mArray;
    private View.OnClickListener mOnClickListener;

    public PickerDialog(Context context, String[] array, View.OnClickListener onClickListener) {
        super(context);
        mContext = context;
        mArray = array;
        mOnClickListener = onClickListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getWindow() != null) {
            getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        }
        setContentView(R.layout.dialog_picker);

        NumberPicker numberPicker = findViewById(R.id.numberPicker);
        TextView tvDone = findViewById(R.id.tvDone);

        numberPicker.setMinValue(0);
        if (mArray.length == 0) {
            return;
        }
        numberPicker.setMaxValue(mArray.length - 1);
        numberPicker.setDisplayedValues(mArray);
        numberPicker.setValue(0); // 設定預設位置
        numberPicker.setWrapSelectorWheel(false); // 是否循環顯示
        numberPicker.setFormatter(new NumberPicker.Formatter() {
            @Override
            public String format(int value) {
                return mArray[value];
            }
        });
        numberPicker.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {
                tvDone.setTag(numberPicker.getValue());
            }
        });
        tvDone.setTag(numberPicker.getValue());
        tvDone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnClickListener != null) {
                    mOnClickListener.onClick(v);
                }
                dismiss();
            }
        });
    }
}
