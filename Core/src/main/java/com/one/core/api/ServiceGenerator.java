package com.one.core.api;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class ServiceGenerator {

    public final static int TIME_OUT_SHORT = 10;
    public final static int TIME_OUT_MEDIUM = 30;
    public final static int TIME_OUT_LONG = 120;
    private final static boolean isRetry = false;

    private static String BASE_URL;

    private static OkHttpClient.Builder httpClient = new OkHttpClient.Builder();

    private static Retrofit.Builder builder;

    private static Retrofit retrofit;

    public static void setBaseUrl(String baseUrl) {
        BASE_URL = baseUrl;
    }

    public static <S> S createService(Class<S> serviceClass) {

        OkHttpClient.Builder httpClientBuilder = new OkHttpClient.Builder()
                .retryOnConnectionFailure(isRetry) // 默認重試一次，若需要重試N次，則要實現攔截器。
                .connectTimeout(TIME_OUT_SHORT, TimeUnit.SECONDS)
                .readTimeout(TIME_OUT_SHORT, TimeUnit.SECONDS)
                .writeTimeout(TIME_OUT_SHORT, TimeUnit.SECONDS);
        OInterceptor loggingInterceptor = new OInterceptor();
        loggingInterceptor.setLevel(OInterceptor.Level.BODY);
//                httpClientBuilder.addInterceptor(loggingInterceptor);
        httpClientBuilder.interceptors().add(loggingInterceptor);

        Gson gson = new GsonBuilder()
                .setPrettyPrinting()
                .serializeNulls()
                .create();

        Retrofit.Builder builder = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .client(httpClientBuilder.build());

        retrofit = builder.client(httpClientBuilder.build()).build();
        return retrofit.create(serviceClass);
    }
}
