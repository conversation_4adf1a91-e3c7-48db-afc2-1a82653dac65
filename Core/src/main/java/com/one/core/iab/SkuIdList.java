package com.one.core.iab;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class SkuIdList {

    @SerializedName("inappItemList")
    private List<String> inappItemList;
    @SerializedName("subsItemList")
    private List<String> subsItemList;

    public List<String> getInappItemList() {
        return inappItemList;
    }

    public void setInappItemList(List<String> inappItemList) {
        this.inappItemList = inappItemList;
    }

    public List<String> getSubsItemList() {
        return subsItemList;
    }

    public void setSubsItemList(List<String> subsItemList) {
        this.subsItemList = subsItemList;
    }
}
