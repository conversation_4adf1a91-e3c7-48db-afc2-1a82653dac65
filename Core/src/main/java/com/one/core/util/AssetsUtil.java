package com.one.core.util;

import android.content.Context;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public class AssetsUtil {

    public static String loadJSONFromAsset(Context context, String fileName) {
        String json;
        try {
            if (context == null) {
                return "";
            }
            InputStream is = context.getAssets().open(fileName);
            int size = is.available();
            byte[] buffer = new byte[size];
            int nRead = is.read(buffer);
            LogUtil.d("" + nRead);
            is.close();
            json = new String(buffer, StandardCharsets.UTF_8);
        } catch (IOException ex) {
            ex.printStackTrace();
            return null;
        }
        return json;
    }
}
