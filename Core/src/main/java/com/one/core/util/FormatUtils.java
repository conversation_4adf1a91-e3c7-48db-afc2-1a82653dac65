package com.one.core.util;

import android.text.format.DateFormat;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class FormatUtils {

    public static String dateToString(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.getDefault());
        return sdf.format(date);
    }

    public static Date stringToDate(String timeString, String format) {
        Date date = new Date();
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(format);//"yyyy/MM/dd HH:mm" , Locale.getDefault()
            date = dateFormat.parse(timeString);
            return date;
        } catch (ParseException ex) {
            ex.printStackTrace();
        }
        return date;
    }

    public static int getInt(String s) {
        return Integer.parseInt(s.replaceAll("[\\D]", ""));
    }

    /**
     * 小數點格式
     */
    public static String getDecimalFormat(double number) {
        //運用DecimalFormat制定好金額顯示格式，每三位數顯示逗號
        DecimalFormat mDecimalFormat = new DecimalFormat("#,###");
        //在format裡轉型成double
        return mDecimalFormat.format(number);
    }

    /**
     * 轉換時間格式
     */
    public static String longToString(long time, String format) {
        Calendar calendar = Calendar.getInstance(Locale.getDefault());
        TimeZone timeZone = TimeZone.getTimeZone("GMT+8");
        calendar.setTimeZone(timeZone);
        calendar.setTimeInMillis(time);
        return DateFormat.format(format, calendar).toString();
    }

    /**
     * Return date in specified format.
     *
     * @param milliSeconds Date in milliseconds
     * @param dateFormat   Date format
     * @return String representing date in specified format
     */
    public static String getMilliSecondsToDate(long milliSeconds, String dateFormat) {
        // Create a DateFormatter object for displaying date in specified format.
        SimpleDateFormat formatter = new SimpleDateFormat(dateFormat, Locale.getDefault());

        // Create a calendar object that will convert the date and time value in milliseconds to date.
        Calendar calendar = Calendar.getInstance();
        TimeZone timeZone = TimeZone.getTimeZone("GMT+8");
        calendar.setTimeZone(timeZone);
        calendar.setTimeInMillis(milliSeconds);
        return formatter.format(calendar.getTime());
    }
}
