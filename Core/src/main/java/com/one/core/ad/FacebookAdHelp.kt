package com.one.core.ad


import android.app.Activity
import android.content.Context
import android.view.View
import android.widget.LinearLayout
import com.facebook.ads.*
import com.facebook.ads.AdSize.BANNER_HEIGHT_50
import com.one.core.data.RemoteConfig
import com.one.core.util.LogUtil


class FacebookAdHelp {
    companion object {

        fun addView(context: Context, adId: String, layout: LinearLayout) {
            if (!RemoteConfig.isOpenAd || RemoteConfig.adType != 2L) {
                return
            }
            val adView = AdView(context, adId, BANNER_HEIGHT_50)
            layout.addView(adView)
            val adListener: AdListener = object : AdListener {
                override fun onError(p0: Ad?, p1: AdError?) {
                    if (p1 != null) {
                        LogUtil.e("onError : " + p1.errorMessage)
                    }
                    adView.visibility = View.GONE
                }

                override fun onAdLoaded(p0: Ad?) {
                    LogUtil.d("onAdLoaded")
                    adView.visibility = View.VISIBLE
                }

                override fun onAdClicked(p0: Ad?) {
                    LogUtil.d("onAdClicked")
                }

                override fun onLoggingImpression(p0: Ad?) {
                    LogUtil.d("onLoggingImpression")
                }
            }
            adView.loadAd(adView.buildLoadAdConfig().withAdListener(adListener).build())
        }

        fun addRewardedAd(context: Activity, adId: String, adCallback: AdCallback) {
            if (!RemoteConfig.isOpenAd || RemoteConfig.adType != 2L) {
                return
            }
            val rewardedVideoAd = RewardedVideoAd(context, adId)
            val rewardedVideoAdListener: RewardedVideoAdListener =
                object : RewardedVideoAdListener {
                    override fun onError(ad: Ad, error: AdError) {
                        // Rewarded video ad failed to load
                        LogUtil.e("Rewarded video ad failed to load: " + error.errorMessage)
                        adCallback.onClosed()
                    }

                    override fun onAdLoaded(ad: Ad) {
                        // Rewarded video ad is loaded and ready to be displayed
                        LogUtil.d("Rewarded video ad is loaded and ready to be displayed!")
                        rewardedVideoAd.show();
                    }

                    override fun onAdClicked(ad: Ad) {
                        // Rewarded video ad clicked
                        LogUtil.d("Rewarded video ad clicked!")
                        adCallback.onClosed()
                    }

                    override fun onLoggingImpression(ad: Ad) {
                        // Rewarded Video ad impression - the event will fire when the
                        // video starts playing
                        LogUtil.d("Rewarded video ad impression logged!")
                        adCallback.onClosed()
                    }

                    override fun onRewardedVideoCompleted() {
                        // Rewarded Video View Complete - the video has been played to the end.
                        // You can use this event to initialize your reward
                        LogUtil.d("Rewarded video completed!")

                        // Call method to give reward
                        // giveReward();
                        adCallback.onCompleted("", 0)
                    }

                    override fun onRewardedVideoClosed() {
                        // The Rewarded Video ad was closed - this can occur during the video
                        // by closing the app, or closing the end card.
                        LogUtil.d("Rewarded video ad closed!")
                        adCallback.onClosed()
                    }
                }
            rewardedVideoAd.loadAd(
                rewardedVideoAd.buildLoadAdConfig()
                    .withAdListener(rewardedVideoAdListener)
                    .build()
            )
        }
    }
}