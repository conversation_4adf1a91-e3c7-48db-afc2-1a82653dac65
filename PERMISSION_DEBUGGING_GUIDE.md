# 權限問題除錯指南

## 問題描述
`isAllowPermissions = false` 導致無法進入主頁面的問題。

## 已修正的問題 ✅

### 1. SplashActivity.kt 權限處理流程
- ✅ 移除舊的 `onRequestPermissionsResult` 方法
- ✅ 添加現代權限處理回調
- ✅ 修正權限檢查時機
- ✅ 添加多重權限檢查點

### 2. 權限檢查流程優化
```kotlin
// SplashActivity.kt 中的權限檢查流程

override fun onCreate(savedInstanceState: Bundle?) {
    hasPermission(true)  // 啟用權限檢查
    super.onCreate(savedInstanceState)
    
    // 檢查點 1: onCreate 中檢查是否已有權限
    if (hasAllRequiredPermissions()) {
        isAllowPermissions = true
        LogUtil.i("SplashActivity: 已有所有權限，直接進入主頁")
    }
}

override fun onResume() {
    super.onResume()
    
    // 檢查點 2: onResume 中再次檢查權限
    if (hasAllRequiredPermissions()) {
        isAllowPermissions = true
        postDelayed()
    }
}

// 檢查點 3: 權限授予回調
override fun showPermissionsAllow() {
    super.showPermissionsAllow()
    LogUtil.i("SplashActivity: 權限已授予，準備進入主頁")
    postDelayed()
}

// 檢查點 4: 權限拒絕時也允許進入（功能受限）
override fun onPermissionDenied() {
    super.onPermissionDenied()
    LogUtil.e("SplashActivity: 權限被拒絕")
    postDelayed()  // 仍然進入主頁
}
```

## 除錯步驟

### 1. 檢查日誌輸出
在 Android Studio 的 Logcat 中查找以下日誌：

```
I/LogUtil: SplashActivity: 已有所有權限，直接進入主頁
I/LogUtil: SplashActivity: 權限已授予，準備進入主頁
E/LogUtil: SplashActivity: 權限被拒絕
E/LogUtil: SplashActivity: 權限被永久拒絕
```

### 2. 檢查權限狀態
在 SplashActivity 中添加除錯代碼：

```kotlin
override fun onResume() {
    super.onResume()
    LogUtil.d("onResume")
    
    // 除錯：檢查權限狀態
    val requiredPermissions = getRequiredPermissions()
    requiredPermissions.forEach { permission ->
        val hasPermission = hasPermission(permission)
        LogUtil.d("權限檢查: $permission = $hasPermission")
    }
    
    LogUtil.d("isAllowPermissions = $isAllowPermissions")
    LogUtil.d("hasAllRequiredPermissions() = ${hasAllRequiredPermissions()}")
    
    FCMUtil.checkGooglePlayServices(this)
    if (hasAllRequiredPermissions()) {
        isAllowPermissions = true
        postDelayed()
    }
}
```

### 3. 手動測試權限流程
1. **清除應用資料**: 在設定中清除應用的所有資料
2. **重新安裝應用**: 確保權限狀態重置
3. **觀察權限對話框**: 檢查是否顯示權限請求對話框
4. **測試不同選擇**:
   - 允許權限
   - 拒絕權限
   - 拒絕並勾選"不再詢問"

### 4. 檢查 AndroidManifest.xml
確保所需權限已正確聲明：

```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

## 常見問題與解決方案

### 問題 1: 權限對話框不顯示
**可能原因**:
- 權限已經被授予
- 權限已經被永久拒絕
- AndroidManifest.xml 中沒有聲明權限

**解決方案**:
```kotlin
// 在 SplashActivity 中添加除錯代碼
override fun onCreate(savedInstanceState: Bundle?) {
    // ... 其他代碼
    
    // 除錯：檢查權限請求是否被觸發
    LogUtil.d("mIsCheckPermission = $mIsCheckPermission")
    LogUtil.d("hasAllRequiredPermissions() = ${hasAllRequiredPermissions()}")
}
```

### 問題 2: isAllowPermissions 始終為 false
**可能原因**:
- 權限回調沒有被正確觸發
- 權限檢查邏輯有問題

**解決方案**:
```kotlin
// 在多個地方設定 isAllowPermissions
override fun onResume() {
    super.onResume()
    
    // 強制檢查並設定權限狀態
    if (hasAllRequiredPermissions()) {
        isAllowPermissions = true
        LogUtil.i("強制設定 isAllowPermissions = true")
        postDelayed()
    } else {
        LogUtil.e("缺少權限，isAllowPermissions = false")
    }
}
```

### 問題 3: 應用卡在 SplashActivity
**可能原因**:
- `postDelayed()` 沒有被調用
- 權限檢查進入無限循環

**解決方案**:
```kotlin
// 添加超時機制
private var timeoutHandler: Handler? = null

override fun onCreate(savedInstanceState: Bundle?) {
    // ... 其他代碼
    
    // 設定超時機制（10秒後強制進入主頁）
    timeoutHandler = Handler()
    timeoutHandler?.postDelayed({
        LogUtil.e("超時強制進入主頁")
        isAllowPermissions = true
        postDelayed()
    }, 10000)
}

override fun onDestroy() {
    super.onDestroy()
    timeoutHandler?.removeCallbacksAndMessages(null)
}
```

## 測試檢查清單

### 基本功能測試
- [ ] 首次安裝應用時顯示權限對話框
- [ ] 授予權限後能正常進入主頁
- [ ] 拒絕權限後仍能進入主頁（功能受限）
- [ ] 永久拒絕權限後仍能進入主頁

### 邊界情況測試
- [ ] 應用在背景時權限被手動修改
- [ ] 系統記憶體不足時的權限處理
- [ ] 應用重啟後的權限狀態
- [ ] 多次快速啟動應用

### 日誌檢查
- [ ] 權限檢查日誌正確輸出
- [ ] `isAllowPermissions` 狀態變化日誌
- [ ] 權限回調方法被正確調用

## 緊急修復方案

如果權限問題仍然存在，可以使用以下緊急修復方案：

```kotlin
// 在 SplashActivity.onResume() 中添加
override fun onResume() {
    super.onResume()
    LogUtil.d("onResume")
    FCMUtil.checkGooglePlayServices(this)
    
    // 緊急修復：直接設定為 true 並進入主頁
    // TODO: 移除此緊急修復，解決根本問題
    isAllowPermissions = true
    postDelayed()
    
    /* 原有的權限檢查邏輯
    if (hasAllRequiredPermissions()) {
        isAllowPermissions = true
        postDelayed()
    }
    */
}
```

## 長期解決方案

### 1. 實現更強健的權限處理
- 添加權限說明對話框
- 實現引導用戶到設定頁面的功能
- 添加權限狀態持久化

### 2. 改進用戶體驗
- 提供無權限模式的功能
- 添加權限教育頁面
- 實現漸進式權限請求

### 3. 添加監控和分析
- 添加權限授予率統計
- 監控權限相關的崩潰
- 分析用戶權限行為

## 聯絡支援

如果問題仍然存在，請提供以下資訊：
1. 完整的 Logcat 日誌
2. 測試設備資訊（Android 版本、製造商）
3. 重現步驟
4. 預期行為 vs 實際行為
